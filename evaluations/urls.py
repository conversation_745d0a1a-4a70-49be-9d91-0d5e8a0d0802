# -*- coding: utf-8 -*-
"""
考评应用URL配置
包含考评管理和匿名评分相关的路由
"""

from django.urls import path, include
from . import views
from .views import wizard_views

app_name = 'evaluations'

# 管理端URL配置
admin_urlpatterns = [
    # 考评模板管理
    path('templates/', views.TemplateListView.as_view(), name='template_list'),
    path('templates/create/', views.TemplateCreateView.as_view(), name='template_create'),
    path('templates/<int:pk>/', views.TemplateDetailView.as_view(), name='template_detail'),
    path('templates/<int:pk>/edit/', views.TemplateUpdateView.as_view(), name='template_update'),
    path('templates/<int:pk>/delete/', views.TemplateDeleteView.as_view(), name='template_delete'),
    path('templates/<int:pk>/copy/', views.TemplateCopyView.as_view(), name='template_copy'),
    
    # 权重规则管理
    path('rules/', views.WeightingRuleListView.as_view(), name='rule_list'),
    path('rules/create/', views.WeightingRuleCreateView.as_view(), name='rule_create'),
    path('rules/<int:pk>/edit/', views.WeightingRuleUpdateView.as_view(), name='rule_update'),
    path('rules/<int:pk>/delete/', views.WeightingRuleDeleteView.as_view(), name='rule_delete'),
    path('rules/<int:pk>/toggle/', views.WeightingRuleToggleView.as_view(), name='rule_toggle'),
    path('rules/<int:pk>/copy/', views.WeightingRuleCopyView.as_view(), name='rule_copy'),
    
    # 考评批次管理
    path('batches/', views.BatchListView.as_view(), name='batch_list'),
    path('batches/create/', views.BatchCreateView.as_view(), name='batch_create'),
    path('batches/<int:pk>/', views.BatchDetailView.as_view(), name='batch_detail'),
    path('batches/<int:pk>/edit/', views.BatchUpdateView.as_view(), name='batch_update'),
    path('batches/<int:pk>/delete/', views.BatchDeleteView.as_view(), name='batch_delete'),
    path('batches/<int:pk>/activate/', views.BatchActivateView.as_view(), name='batch_activate'),
    path('batches/<int:pk>/complete/', views.BatchCompleteView.as_view(), name='batch_complete'),
    path('batches/<int:batch_id>/templates/', views.BatchTemplateConfigView.as_view(), name='batch_template_config'),
    
    # 智能分配
    path('batches/<int:pk>/assign/', views.BatchAssignView.as_view(), name='batch_assign'),
    path('batches/<int:pk>/relations/', views.BatchRelationListView.as_view(), name='batch_relations'),
    
    # 考评关系管理
    path('relations/', views.RelationListView.as_view(), name='relation_list'),
    path('relations/create/', views.RelationCreateView.as_view(), name='relation_create'),
    path('relations/<int:pk>/edit/', views.RelationUpdateView.as_view(), name='relation_update'),
    path('relations/<int:pk>/delete/', views.RelationDeleteView.as_view(), name='relation_delete'),
    
    # 考评进度
    path('progress/', views.ProgressListView.as_view(), name='progress_list'),
    path('progress/<int:batch_id>/', views.ProgressDetailView.as_view(), name='progress_detail'),
    path('progress/<int:batch_id>/participant-tasks/', views.ParticipantTasksView.as_view(), name='participant_tasks'),
    path('progress/<int:batch_id>/send-reminder/', views.SendIndividualReminderView.as_view(), name='send_individual_reminder'),
    path('progress/<int:batch_id>/export-detail/', views.ExportBatchDetailView.as_view(), name='export_batch_detail'),
    path('progress/export/', views.ProgressExportView.as_view(), name='export_progress'),
    
    # 考评历史管理
    path('history/', include('evaluations.urls_history', namespace='history')),

    # 向导系统
    path('wizard/', include([
        # 向导仪表板
        path('', wizard_views.wizard_dashboard_view, name='wizard_dashboard'),
        path('help/', wizard_views.help_center_view, name='help_center'),

        # 权重配置向导
        path('weight/<int:batch_id>/', wizard_views.weight_wizard_view, name='weight_wizard'),
        path('weight-preview/', wizard_views.weight_preview_api, name='weight_preview_api'),
        path('apply-weight/', wizard_views.apply_weight_api, name='apply_weight_api'),

        # 模板创建向导
        path('template/', wizard_views.template_wizard_view, name='template_wizard'),
        path('template-preview/', wizard_views.template_preview_api, name='template_preview_api'),
        path('template-recommendation/', wizard_views.template_recommendation_api, name='template_recommendation_api'),
        path('create-template/', wizard_views.create_template_api, name='create_template_api'),

        # 分配策略向导
        path('assignment/<int:batch_id>/', wizard_views.assignment_wizard_view, name='assignment_wizard'),
        path('assignment-preview/', wizard_views.assignment_preview_api, name='assignment_preview_api'),
        path('execute-assignment/', wizard_views.execute_assignment_api, name='execute_assignment_api'),
    ])),
]

# 匿名端URL配置
anonymous_urlpatterns = [
    path('', views.AnonymousHomeView.as_view(), name='home'),
    path('tasks/', views.AnonymousTaskListView.as_view(), name='task_list'),
    path('evaluate/<int:relation_id>/', views.AnonymousEvaluateView.as_view(), name='evaluate'),
    path('submit/<int:relation_id>/', views.AnonymousSubmitView.as_view(), name='submit'),
    path('draft/<int:relation_id>/', views.AnonymousDraftView.as_view(), name='draft'),
    path('results/', views.AnonymousResultsView.as_view(), name='results'),
]

urlpatterns = [
    path('admin/', include((admin_urlpatterns, 'admin'))),
    path('anonymous/', include((anonymous_urlpatterns, 'anonymous'))),
]
# -*- coding: utf-8 -*-
"""
组织架构应用视图
包含仪表板、认证、部门、职位、员工管理功能
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.views.generic import View, ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy, reverse
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.db import models
from django.utils import timezone
from django.contrib.auth.hashers import make_password, check_password
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import pandas as pd
import json
import logging
import time
import uuid
import hashlib

from .models import Department, Position, Staff, StaffLoginLog
from .middleware import require_admin_permission, require_anonymous_login
from common.models import AuditLog
from common.security.permissions import (
    Permission, Role, require_permission, require_role,
    require_department_access, require_self_or_permission,
    permission_manager
)
from evaluations.models import EvaluationBatch, EvaluationRelation
from reports.models import EvaluationProgress

logger = logging.getLogger(__name__)


class DashboardView(View):
    """
    管理后台仪表板
    显示系统整体数据统计和快捷操作
    """
    template_name = 'admin/dashboard.html'
    
    def dispatch(self, request, *args, **kwargs):
        # 检查用户是否已认证且为管理员
        if not getattr(request, 'is_authenticated', False) or not request.current_staff:
            return redirect('organizations:admin:login')
        
        if not request.current_staff.is_manager:
            messages.error(request, '您没有访问管理后台的权限')
            return redirect('organizations:admin:login')
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """渲染仪表板页面"""
        # 基础统计数据
        stats = self._get_enhanced_dashboard_stats()
        
        # 最近活动数据
        recent_activities = self._get_recent_activities()
        
        # 考评进度数据
        evaluation_progress = self._get_evaluation_progress()
        
        context = {
            'stats': stats,
            'recent_activities': recent_activities,
            'evaluation_progress': evaluation_progress,
            'current_staff': request.current_staff,
        }
        
        return render(request, self.template_name, context)
        
    def _get_enhanced_dashboard_stats(self):
        """获取增强仪表板统计数据"""
        try:
            total_staff = Staff.objects.filter(deleted_at__isnull=True).count()
            secure_codes = Staff.objects.filter(
                deleted_at__isnull=True,
                new_anonymous_code__isnull=False
            ).count()
            
            stats = {
                'total_staff': total_staff,
                'secure_codes': secure_codes,
                'secure_percentage': int((secure_codes / total_staff * 100)) if total_staff > 0 else 0,
                'active_departments': Department.objects.filter(
                    deleted_at__isnull=True, is_active=True
                ).count(),
                'active_batches': 0,  # 暂时设为0，等待evaluations应用实现
            }
            
            return stats
        except Exception as e:
            logger.error(f"获取增强仪表板统计数据失败: {e}")
            return {
                'total_staff': 0,
                'secure_codes': 0,
                'secure_percentage': 0,
                'active_departments': 0,
                'active_batches': 0,
            }
            
    def _get_dashboard_stats(self):
        """获取仪表板基础统计数据（保留兼容性）"""
        try:
            stats = {
                'total_departments': Department.objects.filter(deleted_at__isnull=True).count(),
                'total_positions': Position.objects.filter(deleted_at__isnull=True).count(),
                'total_staff': Staff.objects.filter(deleted_at__isnull=True).count(),
                'active_batches': 0,  # 暂时设为0，等待evaluations应用实现
                'completed_evaluations': 0,  # 暂时设为0，等待evaluations应用实现
            }
            
            # 计算今日新增数据
            today = timezone.now().date()
            stats['today_new_staff'] = Staff.objects.filter(
                created_at__date=today, deleted_at__isnull=True
            ).count()
            
            return stats
        except Exception as e:
            logger.error(f"获取仪表板统计数据失败: {e}")
            return {}
            
    def _get_recent_activities(self):
        """获取最近活动记录"""
        try:
            return AuditLog.objects.filter(
                deleted_at__isnull=True
            ).order_by('-created_at')[:10]
        except Exception as e:
            logger.error(f"获取最近活动记录失败: {e}")
            return []
            
    def _get_evaluation_progress(self):
        """获取考评进度数据"""
        try:
            active_batches = EvaluationBatch.objects.filter(
                status='active', deleted_at__isnull=True
            )
            
            progress_data = []
            for batch in active_batches:
                progress = EvaluationProgress.objects.filter(
                    batch=batch, deleted_at__isnull=True
                ).first()
                
                if progress:
                    progress_data.append({
                        'batch_name': batch.name,
                        'total_relations': progress.total_relations,
                        'completed_relations': progress.completed_relations,
                        'completion_rate': progress.completion_rate,
                    })
                    
            return progress_data
        except Exception as e:
            logger.error(f"获取考评进度数据失败: {e}")
            return []


@method_decorator(csrf_exempt, name='dispatch')
class LoginView(View):
    """
    管理端登录视图（支持JWT）
    支持用户名/员工编号 + 密码登录
    API请求免除CSRF验证
    """
    template_name = 'admin/login_simple.html'  # 使用简化登录模板
    
    def get(self, request):
        """显示登录页面"""
        # 如果已登录，重定向到仪表板
        if hasattr(request, 'is_authenticated') and request.is_authenticated:
            return redirect('organizations:admin:dashboard')
            
        return render(request, self.template_name)
        
    def post(self, request):
        """处理登录请求"""
        # 处理JSON和表单请求
        if request.content_type == 'application/json':
            try:
                data = json.loads(request.body)
                username = data.get('username', '').strip()
                password = data.get('password', '')
                use_jwt = data.get('use_jwt', 'true') == 'true'
            except (json.JSONDecodeError, UnicodeDecodeError):
                return JsonResponse({'success': False, 'error': '请求数据格式错误'}, status=400)
        else:
            username = request.POST.get('username', '').strip()
            password = request.POST.get('password', '')
            use_jwt = request.POST.get('use_jwt', 'true') == 'true'  # 新增：是否使用JWT
        
        if not username or not password:
            return self._error_response('请输入用户名和密码')
            
        try:
            # 查找用户（支持用户名或员工编号登录）
            staff = Staff.objects.filter(
                Q(username=username) | Q(employee_no=username),
                deleted_at__isnull=True,
                is_active=True
            ).first()
            
            if not staff:
                # 记录失败尝试
                self._record_failed_attempt(username, request)
                return self._error_response('用户名或密码错误')
            
            # 检查账户锁定状态
            if staff.is_account_locked():
                return self._error_response(f'账户已锁定，请{staff.get_unlock_time()}后重试')
                
            # 验证密码
            if not staff.check_password(password):
                staff.increment_failed_attempts()
                staff.log_security_event('failed_login', f'密码错误，来源IP: {self._get_client_ip(request)}', self._get_client_ip(request))
                return self._error_response('用户名或密码错误')
                
            # 检查管理权限
            if not staff.is_manager:
                staff.log_security_event('permission_denied', f'非管理员尝试登录管理后台，来源IP: {self._get_client_ip(request)}', self._get_client_ip(request))
                return self._error_response('您没有访问管理后台的权限')
            
            # 检查是否需要强制修改密码
            if staff.should_force_password_change():
                return self._error_response('请先修改密码后再登录')
            
            # 重置失败尝试计数
            staff.reset_failed_attempts()
            
            # 生成认证凭据
            if use_jwt:
                # 检查是否可以生成token
                can_generate, message = staff.can_generate_token()
                if not can_generate:
                    return self._error_response(message)
                
                # JWT认证
                from common.security.jwt_auth import JWTAuthentication
                tokens = JWTAuthentication.generate_tokens(staff)
                
                # 记录登录日志
                self._create_login_log(staff, request, 'admin_jwt')
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=staff.username,
                    action='login',
                    target_model='staff',
                    target_id=staff.id,
                    description=f'管理员 {staff.name} 使用JWT登录系统',
                    ip_address=self._get_client_ip(request)
                )
                
                # 更新最后登录时间
                staff.update_last_login()
                
                # 返回JWT token
                if self._is_api_request(request):
                    return JsonResponse({
                        'success': True,
                        'message': f'欢迎回来，{staff.name}！',
                        'tokens': tokens,
                        'user_info': {
                            'id': staff.id,
                            'name': staff.name,
                            'username': staff.username,
                            'role': staff.role,
                            'department': staff.department.name if staff.department else None,
                            'is_manager': staff.is_manager,
                        }
                    })
                else:
                    # 传统表单提交，设置cookie和session（兼容性）
                    request.session['staff_id'] = staff.id
                    request.session['admin_login'] = True
                    request.session.set_expiry(settings.SESSION_COOKIE_AGE if hasattr(settings, 'SESSION_COOKIE_AGE') else 28800)  # 8小时
                    
                    response = redirect('organizations:admin:dashboard')
                    response.set_cookie(
                        'access_token', 
                        tokens['access_token'],
                        max_age=tokens['expires_in'],
                        httponly=True,
                        secure=request.is_secure(),
                        samesite='Lax'
                    )
                    response.set_cookie(
                        'refresh_token',
                        tokens['refresh_token'],
                        max_age=7*24*3600,  # 7天
                        httponly=True,
                        secure=request.is_secure(),
                        samesite='Lax'
                    )
                    messages.success(request, f'欢迎回来，{staff.name}！')
                    return response
            else:
                # 传统Session认证（兼容性支持）
                request.session['staff_id'] = staff.id
                request.session['admin_login'] = True
                request.session.set_expiry(settings.SESSION_COOKIE_AGE if hasattr(settings, 'SESSION_COOKIE_AGE') else 86400)
                
                # 记录登录日志
                self._create_login_log(staff, request, 'admin_session')
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=staff.username,
                    action='login',
                    target_model='staff',
                    target_id=staff.id,
                    description=f'管理员 {staff.name} 使用Session登录系统',
                    ip_address=self._get_client_ip(request)
                )
                
                # 更新最后登录时间
                staff.update_last_login()
                
                messages.success(request, f'欢迎回来，{staff.name}！')
                return redirect('organizations:admin:dashboard')
                
        except Exception as e:
            logger.error(f"管理端登录失败: {e}")
            return self._error_response('登录失败，请重试')
    
    def _error_response(self, message):
        """统一错误响应"""
        if self._is_api_request():
            return JsonResponse({'success': False, 'error': message}, status=400)
        else:
            messages.error(self.request, message)
            return render(self.request, self.template_name)
    
    def _is_api_request(self, request=None):
        """判断是否是API请求"""
        req = request or self.request
        return (
            req.content_type == 'application/json' or
            req.headers.get('Accept') == 'application/json' or
            'api' in req.path.lower()
        )
    
    def _record_failed_attempt(self, username, request):
        """记录失败尝试"""
        try:
            staff = Staff.objects.filter(
                Q(username=username) | Q(employee_no=username)
            ).first()
            if staff:
                staff.increment_failed_attempts()
                staff.log_security_event(
                    'failed_login_attempt', 
                    f'用户名: {username}, IP: {self._get_client_ip(request)}',
                    self._get_client_ip(request)
                )
        except Exception as e:
            logger.error(f"记录失败尝试失败: {e}")
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip
            
    def _create_login_log(self, staff, request, login_type):
        """创建登录日志"""
        try:
            # 将admin登录类型转换为normal
            log_type = 'normal' if login_type == 'admin' else login_type
            StaffLoginLog.objects.create(
                staff=staff,
                login_type=log_type,
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
        except Exception as e:
            logger.error(f"创建登录日志失败: {e}")
            
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


@method_decorator(csrf_exempt, name='dispatch')
class TokenRefreshView(View):
    """
    JWT Token刷新视图
    处理访问token的刷新请求
    API请求免除CSRF验证
    """
    
    def post(self, request):
        """处理token刷新请求"""
        try:
            # 从请求中获取refresh token
            refresh_token = None
            
            if request.content_type == 'application/json':
                import json
                try:
                    data = json.loads(request.body)
                    refresh_token = data.get('refresh_token')
                except (json.JSONDecodeError, UnicodeDecodeError):
                    return JsonResponse({'success': False, 'error': '请求数据格式错误'}, status=400)
            else:
                # 表单提交或从cookie获取
                refresh_token = request.POST.get('refresh_token') or request.COOKIES.get('refresh_token')
            
            if not refresh_token:
                return JsonResponse({'success': False, 'error': '缺少refresh token'}, status=400)
            
            # 刷新access token
            from common.security.jwt_auth import JWTAuthentication
            new_tokens = JWTAuthentication.refresh_access_token(refresh_token)
            
            # 返回新的access token
            if self._is_api_request(request):
                return JsonResponse({
                    'success': True,
                    'tokens': new_tokens,
                    'message': 'Token刷新成功'
                })
            else:
                # 表单提交，更新cookie
                response = JsonResponse({
                    'success': True,
                    'message': 'Token刷新成功'
                })
                response.set_cookie(
                    'access_token',
                    new_tokens['access_token'],
                    max_age=new_tokens['expires_in'],
                    httponly=True,
                    secure=request.is_secure(),
                    samesite='Lax'
                )
                return response
                
        except Exception as e:
            logger.error(f"Token刷新失败: {e}")
            import traceback
            logger.error(f"Token刷新详细错误: {traceback.format_exc()}")
            return JsonResponse({
                'success': False,
                'error': f'Token刷新失败: {str(e)}',
                'code': 'TOKEN_REFRESH_FAILED'
            }, status=400)
    
    def _is_api_request(self, request):
        """判断是否是API请求"""
        return (
            request.content_type == 'application/json' or
            request.headers.get('Accept') == 'application/json' or
            'api' in request.path.lower()
        )


@method_decorator(csrf_exempt, name='dispatch')
class LogoutView(View):
    """
    管理端登出视图（支持JWT）
    清除会话并撤销JWT token
    API请求免除CSRF验证
    """
    
    def get(self, request):
        """处理登出请求"""
        return self._handle_logout(request)
    
    def post(self, request):
        """处理POST登出请求"""
        return self._handle_logout(request)
    
    def _handle_logout(self, request):
        """统一处理登出逻辑"""
        try:
            # 记录登出日志和撤销token
            if hasattr(request, 'current_staff') and request.current_staff:
                staff = request.current_staff
                auth_method = getattr(request, 'auth_method', 'session')
                
                # JWT登出：撤销token
                if auth_method == 'jwt':
                    self._revoke_jwt_tokens(request, staff)
                    
                    # 更新JWT登录记录的登出时间
                    login_log = StaffLoginLog.objects.filter(
                        staff=staff,
                        login_type__in=['normal', 'admin_jwt'],
                        logout_time__isnull=True
                    ).order_by('-created_at').first()
                else:
                    # Session登出：更新session登录记录
                    login_log = StaffLoginLog.objects.filter(
                        staff=staff,
                        login_type__in=['normal', 'admin_session'],
                        logout_time__isnull=True
                    ).order_by('-created_at').first()
                
                if login_log:
                    login_log.logout_time = timezone.now()
                    login_log.save(update_fields=['logout_time'])
                    
                # 记录审计日志
                AuditLog.objects.create(
                    user=staff.username,
                    action='logout',
                    target_model='staff',
                    target_id=staff.id,
                    description=f'管理员 {staff.name} 退出系统 (方式: {auth_method})',
                    ip_address=self._get_client_ip(request)
                )
                
                # 记录安全事件
                staff.log_security_event(
                    'logout',
                    f'用户登出，认证方式: {auth_method}，IP: {self._get_client_ip(request)}',
                    self._get_client_ip(request)
                )
                
            # 清除会话（兼容性支持）
            if hasattr(request, 'session') and request.session:
                try:
                    request.session.flush()
                except Exception as e:
                    logger.warning(f"清除session失败: {e}")  # 对于JWT请求，这是正常的
            
            # 根据请求类型返回响应
            if self._is_api_request(request):
                response = JsonResponse({
                    'success': True,
                    'message': '您已成功退出系统'
                })
            else:
                # 兼容性支持：传统浏览器请求
                if hasattr(request, '_messages'):
                    try:
                        messages.success(request, '您已成功退出系统')
                    except Exception as e:
                        logger.warning(f"设置消息失败: {e}")
                response = redirect('organizations:admin:login')
            
            # 清除JWT cookies
            self._clear_jwt_cookies(response)
            
            return response
            
        except Exception as e:
            logger.error(f"管理端登出失败: {e}")
            
            if self._is_api_request(request):
                return JsonResponse({
                    'success': False,
                    'error': '登出失败，请重试'
                }, status=500)
            else:
                messages.error(request, '登出失败，请重试')
                return redirect('organizations:admin:login')
    
    def _revoke_jwt_tokens(self, request, staff):
        """撤销JWT tokens"""
        try:
            from common.security.jwt_auth import JWTAuthentication, extract_token_from_header
            
            # 获取当前token ID
            token_id = None
            auth_header = request.META.get('HTTP_AUTHORIZATION')
            
            if auth_header:
                token = extract_token_from_header(auth_header)
            else:
                token = request.COOKIES.get('access_token')
            
            if token:
                from common.security.jwt_auth import JWTAuthentication
                token_info = JWTAuthentication.get_token_info(token)
                if token_info:
                    token_id = token_info.get('token_id')
            
            # 撤销特定token或所有token
            if token_id:
                JWTAuthentication.revoke_token(staff.id, token_id)
            else:
                JWTAuthentication.revoke_token(staff.id)
                
            logger.info(f"已撤销用户 {staff.username} 的JWT token")
            
        except Exception as e:
            logger.error(f"撤销JWT token失败: {e}")
    
    def _clear_jwt_cookies(self, response):
        """清除JWT相关的cookies"""
        try:
            response.delete_cookie('access_token')
            response.delete_cookie('refresh_token')
        except Exception as e:
            logger.error(f"清除JWT cookies失败: {e}")
    
    def _is_api_request(self, request):
        """判断是否是API请求"""
        return (
            request.content_type == 'application/json' or
            request.headers.get('Accept') == 'application/json' or
            'api' in request.path.lower()
        )
        
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip


# 部门管理视图类
class DepartmentListView(ListView):
    """部门列表视图"""
    model = Department
    template_name = 'admin/department/list.html'
    context_object_name = 'departments'
    paginate_by = 20

    @method_decorator(require_permission(Permission.ORG_VIEW_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_template_names(self):
        """根据请求参数选择模板，默认使用表格视图"""
        view_type = self.request.GET.get('view', 'table')  # 默认改为表格视图
        if view_type == 'card':
            return ['admin/department/list.html']
        return ['admin/department/list_table.html']

    def get_queryset(self):
        """获取部门列表数据"""
        queryset = Department.objects.filter(deleted_at__isnull=True).order_by('-created_at')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(dept_code__icontains=search) | Q(name__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        """添加统计数据到上下文"""
        context = super().get_context_data(**kwargs)

        # 计算统计数据
        all_departments = Department.objects.filter(deleted_at__isnull=True)
        context['stats'] = {
            'total_departments': all_departments.count(),
            'active_departments': all_departments.filter(is_active=True).count(),
            'total_staff': Staff.objects.filter(deleted_at__isnull=True, is_active=True).count(),
            'departments_with_staff': all_departments.filter(
                staff__deleted_at__isnull=True,
                staff__is_active=True
            ).distinct().count(),
        }

        return context


class DepartmentCreateView(CreateView):
    """部门创建视图"""
    model = Department
    template_name = 'admin/department/create.html'
    fields = ['dept_code', 'name', 'parent_department', 'manager', 'description', 'is_active', 'sort_order']
    success_url = reverse_lazy('organizations:admin:department_list')

    @method_decorator(require_permission(Permission.ORG_CREATE_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)

        # 获取可选择的上级部门（排除已删除的）
        context['parent_departments'] = Department.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).order_by('dept_code')

        # 获取可选择的部门经理（活跃员工）
        context['managers'] = Staff.objects.filter(
            is_active=True,
            deleted_at__isnull=True
        ).select_related('department').order_by('department__dept_code', 'name')

        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.created_by = self.request.current_staff
        messages.success(self.request, f'部门 "{form.instance.name}" 创建成功！')
        return super().form_valid(form)

    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '部门创建失败，请检查输入信息')
        return super().form_invalid(form)


# 匿名端视图类
class AnonymousLoginView(View):
    """
    匿名端登录视图
    支持新旧匿名编号向后兼容登录
    """
    template_name = 'anonymous/login.html'
    
    def get(self, request):
        """显示匿名登录页面"""
        return render(request, self.template_name)
        
    def post(self, request):
        """处理匿名登录（支持新旧编号）"""
        anonymous_code = request.POST.get('anonymous_code', '').strip().upper()
        
        if not anonymous_code:
            messages.error(request, '请输入匿名编号')
            return render(request, self.template_name)
            
        try:
            # 使用安全验证器验证编号
            from common.security.anonymous import AnonymousCodeValidator
            validator = AnonymousCodeValidator()
            
            is_valid, error_message, staff = validator.validate_login_code(anonymous_code)
            
            if not is_valid:
                messages.error(request, error_message)
                
                # 记录失败的登录尝试
                self._log_failed_login_attempt(anonymous_code, request, error_message)
                
                return render(request, self.template_name)
            
            # 检查是否使用的是旧编号
            using_old_code = (staff.anonymous_code == anonymous_code and 
                            staff.new_anonymous_code and 
                            staff.new_anonymous_code != anonymous_code)
            
            # 设置匿名登录会话
            request.session['staff_id'] = staff.id
            request.session['anonymous_login'] = True
            request.session['anonymous_code_used'] = anonymous_code
            request.session['using_old_code'] = using_old_code
            request.session.set_expiry(3600)  # 1小时过期
            
            # 记录登录日志
            self._create_login_log(staff, request, 'anonymous', using_old_code)
            
            # 成功消息
            if using_old_code:
                messages.success(
                    request, 
                    f'欢迎，{staff.name}！您使用的是旧版编号，建议更换为新编号以提高安全性。'
                )
            else:
                messages.success(request, f'欢迎，{staff.name}！')
            
            return redirect('evaluations:anonymous:home')
            
        except Exception as e:
            logger.error(f"匿名登录失败: {e}", exc_info=True)
            messages.error(request, '系统错误，请稍后重试')
            return render(request, self.template_name)
            
    def _create_login_log(self, staff, request, login_type, using_old_code=False):
        """创建登录日志（增强版）"""
        try:
            # 将admin登录类型转换为normal
            log_type = 'normal' if login_type == 'admin' else login_type
            
            StaffLoginLog.objects.create(
                staff=staff,
                login_type=log_type,
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                is_success=True
            )
            
            # 记录安全事件（如果使用旧编号）
            if using_old_code:
                staff.log_security_event(
                    'OLD_ANONYMOUS_CODE_USED',
                    f'用户使用旧版匿名编号登录',
                    self._get_client_ip(request)
                )
                
        except Exception as e:
            logger.error(f"创建登录日志失败: {e}")
    
    def _log_failed_login_attempt(self, anonymous_code, request, error_message):
        """记录失败的登录尝试"""
        try:
            # 创建失败的登录日志（不关联具体员工）
            from common.models import AuditLog
            
            AuditLog.objects.create(
                table_name='anonymous_login',
                record_id=0,
                action='LOGIN_FAILED',
                field_name='anonymous_code',
                old_value='',
                new_value=anonymous_code[:10] + '***',  # 部分隐藏编号
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                extra_data={
                    'error_message': error_message,
                    'code_length': len(anonymous_code),
                    'code_format': 'new' if '-' in anonymous_code else 'old'
                }
            )
            
        except Exception as e:
            logger.error(f"记录失败登录尝试失败: {e}")
            
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AnonymousLogoutView(View):
    """匿名端登出视图"""
    
    def get(self, request):
        """处理匿名登出"""
        try:
            # 更新登录记录
            if hasattr(request, 'current_staff') and request.current_staff:
                staff = request.current_staff
                login_log = StaffLoginLog.objects.filter(
                    staff=staff,
                    login_type='anonymous',
                    logout_time__isnull=True
                ).order_by('-login_time').first()
                
                if login_log:
                    login_log.logout_time = timezone.now()
                    login_log.save(update_fields=['logout_time'])
                    
            # 清除会话
            request.session.flush()
            messages.success(request, '您已成功退出系统')
            
        except Exception as e:
            logger.error(f"匿名登出失败: {e}")
            
        return redirect('organizations:anonymous:anonymous_login')


class AnonymousProfileView(View):
    """匿名端个人信息视图"""
    template_name = 'anonymous/profile.html'
    
    @require_anonymous_login
    def get(self, request):
        """显示个人信息页面"""
        context = {
            'staff': request.current_staff,
            'recent_evaluations': self._get_recent_evaluations(request.current_staff),
        }
        return render(request, self.template_name, context)
        
    def _get_recent_evaluations(self, staff):
        """获取最近的考评记录"""
        try:
            return EvaluationRelation.objects.filter(
                evaluator=staff,
                deleted_at__isnull=True
            ).select_related('evaluatee', 'batch', 'template').order_by('-created_at')[:5]
        except Exception as e:
            logger.error(f"获取最近考评记录失败: {e}")
            return []


# 其他视图类（占位实现，后续完善）
class DepartmentDetailView(DetailView):
    model = Department
    template_name = 'admin/department/detail.html'
    
    @method_decorator(require_permission(Permission.ORG_VIEW_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        department = self.get_object()
        
        # 获取部门员工列表
        context['staff_list'] = Staff.objects.filter(
            department=department,
            deleted_at__isnull=True
        ).select_related('position').order_by('name')
        
        # 获取子部门列表
        context['sub_departments'] = Department.objects.filter(
            parent_department=department,
            deleted_at__isnull=True
        ).order_by('sort_order', 'name')
        
        # 获取职位数量
        context['positions_count'] = Position.objects.filter(
            department=department,
            deleted_at__isnull=True
        ).count()
        
        # 统计信息
        context['stats'] = {
            'average_score': 4.2,  # 这里可以后续计算真实的平均评分
        }
        
        return context

class DepartmentUpdateView(UpdateView):
    model = Department
    template_name = 'admin/department/update.html'
    fields = ['dept_code', 'name', 'parent_department', 'manager', 'description', 'is_active', 'sort_order']
    success_url = reverse_lazy('organizations:admin:department_list')

    @method_decorator(require_permission(Permission.ORG_EDIT_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        
        # 获取部门的员工列表（用于选择经理）
        context['department_staff'] = Staff.objects.filter(
            department=self.get_object(),
            is_active=True,
            deleted_at__isnull=True
        ).select_related('position').order_by('name')
        
        # 获取部门统计信息
        department = self.get_object()
        context['staff_count'] = Staff.objects.filter(
            department=department,
            deleted_at__isnull=True
        ).count()
        
        context['staff_list'] = Staff.objects.filter(
            department=department,
            deleted_at__isnull=True
        ).select_related('position')[:10]  # 只显示前10个员工
        
        context['positions_count'] = Position.objects.filter(
            department=department,
            deleted_at__isnull=True
        ).count()
        
        context['sub_departments_count'] = Department.objects.filter(
            parent_department=department,
            deleted_at__isnull=True
        ).count()
        
        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        form.instance.updated_by = self.request.current_staff
        messages.success(self.request, f'部门 "{form.instance.name}" 修改成功！')
        return super().form_valid(form)

    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '部门修改失败，请检查输入信息')
        return super().form_invalid(form)

class DepartmentDeleteView(DeleteView):
    model = Department
    success_url = reverse_lazy('organizations:admin:department_list')

class PositionListView(ListView):
    model = Position
    template_name = 'admin/position/list.html'
    paginate_by = 25

    def get_template_names(self):
        """根据请求参数选择模板，默认使用表格视图"""
        view_type = self.request.GET.get('view', 'table')  # 默认改为表格视图
        if view_type == 'card':
            return ['admin/position/list.html']
        return ['admin/position/list_table.html']

    def get_queryset(self):
        """获取职位列表数据，默认按最新建立时间排序"""
        queryset = Position.objects.filter(deleted_at__isnull=True).order_by('-created_at')
        
        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | 
                Q(position_code__icontains=search) |
                Q(department__name__icontains=search)
            )
        
        # 部门筛选
        department_id = self.request.GET.get('department')
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        
        # 级别筛选
        level = self.request.GET.get('level')
        if level:
            queryset = queryset.filter(level=level)
        
        # 管理岗筛选
        is_management = self.request.GET.get('is_management')
        if is_management:
            queryset = queryset.filter(is_management=is_management == 'true')
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取所有部门用于筛选
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).order_by('dept_code')

        # 使用单次查询获取职位统计数据
        position_stats = Position.objects.filter(deleted_at__isnull=True).aggregate(
            management_count=Count('id', filter=Q(is_management=True)),
            department_manager_count=Count('id', filter=Q(is_department_manager=True)),
            max_level=models.Max('level'),
            departments_covered=Count('department', distinct=True)
        )

        context['management_count'] = position_stats['management_count']
        context['department_manager_count'] = position_stats['department_manager_count']
        context['stats'] = {
            'max_level': position_stats['max_level'] or 0,
            'departments_covered': position_stats['departments_covered']
        }

        return context

class PositionCreateView(CreateView):
    model = Position
    template_name = 'admin/position/create.html'
    fields = ['department', 'position_code', 'name', 'level', 'description', 'is_management', 'is_department_manager', 'is_active', 'sort_order']
    success_url = reverse_lazy('organizations:admin:position_list')
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        messages.success(self.request, f'职位 "{form.instance.name}" 创建成功！')
        return super().form_valid(form)
    
    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '职位创建失败，请检查输入信息')
        return super().form_invalid(form)

class PositionDetailView(DetailView):
    model = Position
    template_name = 'admin/position/detail.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        position = self.get_object()
        
        # 获取该职位的在职员工
        context['staff_list'] = Staff.objects.filter(
            position=position,
            is_active=True,
            deleted_at__isnull=True
        ).select_related('department').order_by('name')
        
        return context

class PositionUpdateView(UpdateView):
    model = Position
    template_name = 'admin/position/update.html'
    fields = ['department', 'position_code', 'name', 'level', 'description', 'is_management', 'is_department_manager', 'is_active', 'sort_order']
    success_url = reverse_lazy('organizations:admin:position_list')
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        messages.success(self.request, f'职位 "{form.instance.name}" 修改成功！')
        return super().form_valid(form)
    
    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '职位修改失败，请检查输入信息')
        return super().form_invalid(form)

class PositionDeleteView(DeleteView):
    model = Position
    success_url = reverse_lazy('organizations:admin:position_list')


class PositionBatchDeleteView(View):
    """职位批量删除视图"""
    
    @method_decorator(require_permission(Permission.ORG_DELETE_POSITION))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request):
        """处理职位批量删除请求"""
        try:
            data = json.loads(request.body)
            position_ids = data.get("position_ids", [])
            
            if not position_ids:
                return JsonResponse({
                    'success': False,
                    'error': '未选择任何职位'
                })
            
            # 获取当前用户
            current_user = getattr(request, 'user', None)
            if not current_user or not hasattr(current_user, 'username'):
                return JsonResponse({
                    'success': False,
                    'error': '用户未认证'
                })
            
            with transaction.atomic():
                # 获取要删除的职位
                positions = Position.objects.filter(id__in=position_ids, deleted_at__isnull=True)
                
                if not positions.exists():
                    return JsonResponse({
                        'success': False,
                        'error': '没有找到可删除的职位'
                    })
                
                # 检查职位依赖关系
                dependencies = self._check_dependencies(positions)
                if dependencies:
                    return JsonResponse({
                        'success': False,
                        'error': f'无法删除这些职位，存在以下依赖关系：{", ".join(dependencies)}'
                    })
                
                # 记录要删除的职位名称（在删除前获取）
                position_names = list(positions.values_list("name", flat=True))
                
                # 执行软删除
                deleted_count = 0
                for position in positions:
                    position.soft_delete(deleted_by=current_user.username)
                    deleted_count += 1
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=current_user.username,
                    action="delete",
                    target_model="Position",
                    description=f"批量删除 {deleted_count} 个职位",
                    extra_data={
                        "position_ids": position_ids,
                        "position_names": position_names,
                        "operator": getattr(current_user, 'name', current_user.username),
                        "batch_operation": True
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                
                return JsonResponse({
                    'success': True,
                    'message': f'成功删除 {deleted_count} 个职位',
                    'deleted_count': deleted_count
                })
                
        except Exception as e:
            logger.error(f"批量删除职位失败: {e}")
            return JsonResponse({
                'success': False,
                'error': f'删除失败: {str(e)}'
            })
    
    def _check_dependencies(self, positions):
        """检查职位删除前的依赖关系"""
        dependencies = []
        
        # 检查是否有员工正在使用这些职位
        from .models import Staff
        staff_with_positions = Staff.objects.filter(
            position__in=positions,
            deleted_at__isnull=True
        ).exists()
        
        if staff_with_positions:
            dependencies.append('存在员工正在使用这些职位')
        
        return dependencies
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class StaffListView(ListView):
    model = Staff
    template_name = 'admin/staff/list.html'
    paginate_by = 25

    @method_decorator(require_permission(Permission.ORG_VIEW_STAFF))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """获取员工列表数据"""
        queryset = Staff.objects.filter(deleted_at__isnull=True).select_related('department', 'position').order_by('-created_at')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(employee_no__icontains=search) |
                Q(username__icontains=search) |
                Q(email__icontains=search) |
                Q(department__name__icontains=search)
            )

        # 部门筛选
        department_id = self.request.GET.get('department')
        if department_id:
            queryset = queryset.filter(department_id=department_id)

        # 角色筛选
        role = self.request.GET.get('role')
        if role:
            queryset = queryset.filter(role=role)

        # 状态筛选
        is_active = self.request.GET.get('is_active')
        if is_active:
            queryset = queryset.filter(is_active=is_active == 'true')

        return queryset

    def get_context_data(self, **kwargs):
        """添加统计数据到上下文"""
        context = super().get_context_data(**kwargs)

        # 使用单次查询获取统计数据
        staff_stats = Staff.objects.filter(deleted_at__isnull=True).aggregate(
            total_staff=Count('id'),
            active_staff=Count('id', filter=Q(is_active=True)),
            admin_staff=Count('id', filter=Q(role__in=['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'admin']))
        )

        context['stats'] = {
            'total_staff': staff_stats['total_staff'],
            'active_staff': staff_stats['active_staff'],
            'admin_staff': staff_stats['admin_staff'],
            'total_departments': Department.objects.filter(deleted_at__isnull=True).count(),
        }

        # 获取所有部门用于筛选
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).order_by('dept_code')

        # 角色选择
        context['role_choices'] = Staff.ROLE_CHOICES

        return context

class StaffCreateView(CreateView):
    model = Staff
    template_name = 'admin/staff/create.html'
    fields = ['username', 'employee_no', 'name', 'department', 'position', 'email', 'phone', 'hire_date', 'role', 'is_active']
    success_url = reverse_lazy('organizations:admin:staff_list')

    @method_decorator(require_permission(Permission.ORG_CREATE_STAFF))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)

        # 获取可选择的部门（排除已删除的）
        context['departments'] = Department.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).order_by('dept_code')

        # 获取可选择的职位（排除已删除的）
        context['positions'] = Position.objects.filter(
            deleted_at__isnull=True,
            is_active=True
        ).order_by('department__dept_code', 'level')

        return context

    def form_valid(self, form):
        """表单验证成功后的处理"""
        try:
            with transaction.atomic():
                # 设置创建者
                form.instance.created_by = self.request.current_staff

                # 生成员工编号（如果未提供）
                if not form.instance.employee_no:
                    form.instance.employee_no = self._generate_employee_no()

                # 生成默认密码（员工编号的SHA256哈希）
                import hashlib
                default_password = hashlib.sha256(form.instance.employee_no.encode()).hexdigest()
                form.instance.password = default_password
                form.instance.force_password_change = True

                # 生成匿名编号
                form.instance.anonymous_code = self._generate_old_anonymous_code()

                # 保存员工
                response = super().form_valid(form)

                # 生成新的安全匿名编号
                try:
                    from common.security.anonymous import SecureAnonymousCodeGenerator
                    generator = SecureAnonymousCodeGenerator()
                    department_id = form.instance.department.id if form.instance.department else 1
                    new_anonymous_code = generator.generate_secure_code(form.instance.id, department_id)

                    # 更新新的安全匿名编号
                    form.instance.new_anonymous_code = new_anonymous_code
                    form.instance.anonymous_code_generated_at = timezone.now()
                    form.instance.anonymous_code_version = 'v2.0'
                    form.instance.save(update_fields=[
                        'new_anonymous_code',
                        'anonymous_code_generated_at',
                        'anonymous_code_version'
                    ])
                except Exception as e:
                    logger.warning(f"生成安全匿名编号失败: {e}")

                messages.success(self.request, f'员工 "{form.instance.name}" 创建成功！')
                return response

        except Exception as e:
            logger.error(f"员工创建失败: {e}")
            messages.error(self.request, '员工创建失败，请重试')
            return self.form_invalid(form)

    def form_invalid(self, form):
        """表单验证失败时的处理"""
        messages.error(self.request, '员工创建失败，请检查输入信息')
        return super().form_invalid(form)

    def _generate_employee_no(self):
        """生成员工编号"""
        import uuid
        today = timezone.now().strftime('%Y%m%d')
        random_suffix = str(uuid.uuid4().int)[:4]
        employee_no = f"EMP{today}{random_suffix}"

        # 确保员工编号唯一
        while Staff.objects.filter(employee_no=employee_no, deleted_at__isnull=True).exists():
            random_suffix = str(uuid.uuid4().int)[:4]
            employee_no = f"EMP{today}{random_suffix}"

        return employee_no

    def _generate_old_anonymous_code(self):
        """生成旧格式匿名编号（兼容性）"""
        import uuid
        anonymous_code = f"A{str(uuid.uuid4().int)[:7]}"
        while Staff.objects.filter(anonymous_code=anonymous_code, deleted_at__isnull=True).exists():
            anonymous_code = f"A{str(uuid.uuid4().int)[:7]}"
        return anonymous_code

class StaffDetailView(DetailView):
    model = Staff
    template_name = 'admin/staff/detail.html'

class StaffUpdateView(UpdateView):
    model = Staff
    template_name = 'admin/staff/update.html'
    fields = ['username', 'employee_no', 'name', 'department', 'position', 'email', 'phone', 'hire_date', 'role', 'is_active']

class StaffDeleteView(View):
    """员工软删除视图"""
    
    @method_decorator(require_permission(Permission.ORG_DELETE_STAFF))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request, pk):
        """处理员工删除请求"""
        try:
            staff = Staff.objects.get(pk=pk, deleted_at__isnull=True)
        except Staff.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': '员工不存在或已被删除'
            })
        
        try:
            # 检查依赖关系
            dependencies = self._check_dependencies(staff)
            if dependencies:
                return JsonResponse({
                    'success': False,
                    'error': f'无法删除该员工，存在以下依赖关系：{", ".join(dependencies)}'
                })
            
            # 执行软删除
            staff.soft_delete(deleted_by=request.user.username if hasattr(request.user, 'username') else 'system')
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.user.username if hasattr(request.user, 'username') else 'system',
                action='delete',
                target_model='Staff',
                target_id=staff.pk,
                description=f'软删除员工: {staff.name} ({staff.employee_no})',
                ip_address=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return JsonResponse({
                'success': True,
                'message': f'员工 "{staff.name}" 删除成功'
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': f'删除失败: {str(e)}'
            })
    
    def _check_dependencies(self, staff):
        """检查员工删除前的依赖关系"""
        dependencies = []
        
        # 检查是否有未完成的考评关系
        from evaluations.models import EvaluationRelation
        active_relations = EvaluationRelation.objects.filter(
            models.Q(evaluator=staff) | models.Q(evaluatee=staff),
            deleted_at__isnull=True,
            batch__status__in=['draft', 'active']
        ).exists()
        
        if active_relations:
            dependencies.append('存在未完成的考评关系')
        
        # 检查是否是部门负责人
        managed_departments = staff.managed_departments.filter(deleted_at__isnull=True).exists()
        if managed_departments:
            dependencies.append('是部门负责人')
        
        return dependencies
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class StaffResetPasswordView(View):
    """员工密码重置视图"""
    template_name = 'admin/staff/reset_password.html'

    @method_decorator(require_permission(Permission.ORG_EDIT_STAFF))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, pk):
        """显示密码重置页面"""
        try:
            staff = Staff.objects.get(pk=pk, deleted_at__isnull=True)
        except Staff.DoesNotExist:
            messages.error(request, '员工不存在')
            return redirect('organizations:admin:staff_list')

        context = {
            'staff': staff,
        }
        return render(request, self.template_name, context)

    def post(self, request, pk):
        """处理密码重置请求"""
        try:
            staff = Staff.objects.get(pk=pk, deleted_at__isnull=True)
        except Staff.DoesNotExist:
            messages.error(request, '员工不存在')
            return redirect('organizations:admin:staff_list')

        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        # 验证密码
        if not new_password or len(new_password) < 6:
            messages.error(request, '密码长度不能少于6位')
            return render(request, self.template_name, {'staff': staff})

        if new_password != confirm_password:
            messages.error(request, '两次输入的密码不一致')
            return render(request, self.template_name, {'staff': staff})

        # 重置密码
        staff.set_password(new_password)
        staff.save()

        # 记录审计日志
        AuditLog.objects.create(
            user=request.current_staff.username,
            action='reset_password',
            target_model='staff',
            target_id=staff.id,
            description=f'重置员工 {staff.name} 的密码',
            ip_address=self._get_client_ip(request)
        )

        messages.success(request, f'员工 {staff.name} 的密码重置成功')
        return redirect('organizations:admin:staff_detail', pk=staff.pk)

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

# Excel导入导出视图
from common.excel_utils import ExcelProcessor, ExcelTemplateGenerator, ExcelImporter
from common.models import ImportHistory
import time


class StaffImportView(View):
    """员工信息导入视图"""
    template_name = 'admin/staff/import.html'
    
    def get(self, request):
        """显示导入页面"""
        # 获取最近的导入历史
        recent_imports = ImportHistory.objects.filter(
            import_type='staff',
            user_id=request.current_staff.id
        )[:10]
        
        context = {
            'recent_imports': recent_imports,
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        """处理导入请求"""
        try:
            if 'file' not in request.FILES:
                return JsonResponse({'success': False, 'error': '请选择要导入的文件'})

            file = request.FILES['file']

            # 验证文件格式
            if not file.name.endswith(('.xlsx', '.xls')):
                return JsonResponse({'success': False, 'error': '只支持Excel文件格式(.xlsx, .xls)'})

            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)

            # 定义字段映射
            field_mapping = {
                '姓名*': 'name',
                '部门*': 'department__name',
                '级别*': 'level',
                '工号': 'employee_no',
                '职位': 'position__name',
                '邮箱': 'email',
                '手机号': 'phone'
            }

            # 定义验证器
            validators = {
                'required_fields': ['姓名*', '部门*', '级别*'],
                'unique_fields': ['工号'],
                'data_types': {
                    '姓名*': 'string',
                    '部门*': 'string',
                    '级别*': 'integer',
                    '工号': 'string',
                    '职位': 'string',
                    '邮箱': 'string',
                    '手机号': 'string'
                }
            }
            
            # 开始计时
            start_time = time.time()
            
            # 创建导入历史记录
            import_history = ImportHistory.objects.create(
                user_id=request.current_staff.id,
                import_type='staff',
                filename=file.name,
                status='processing'
            )
            
            # 使用自定义的员工导入逻辑
            result = self._process_staff_import(file, field_mapping, validators)
            
            # 更新导入历史
            processing_time = time.time() - start_time
            import_history.total_count = result.get('total_rows', 0)
            import_history.success_count = result.get('success_count', 0)
            import_history.error_count = result.get('error_count', 0)
            import_history.processing_time = processing_time
            import_history.error_details = result.get('errors', [])
            import_history.mark_completed()
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='import',
                target_model='Staff',
                description=f'导入员工信息: 成功{result.get("success_count", 0)}条，失败{result.get("error_count", 0)}条',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return JsonResponse(result)
            
        except Exception as e:
            logger.error(f"员工信息导入失败: {e}")
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': '导入过程中发生错误，请重试'
            })

    def _process_staff_import(self, file, field_mapping, validators):
        """自定义员工导入处理逻辑"""
        import pandas as pd
        import time
        from io import BytesIO
        from common.models import ImportHistory

        try:
            # 将Django UploadedFile转换为BytesIO
            file_buffer = BytesIO(file.read())

            # 创建导入器
            importer = ExcelImporter(file_buffer)

            # 读取Excel
            df = importer.read_excel()

            # 验证表头
            required_headers = list(field_mapping.keys())
            if not importer.validate_headers(required_headers):
                return importer.get_validation_summary()

            # 数据验证
            if validators:
                if 'data_types' in validators:
                    importer.validate_data_types(validators['data_types'])

                if 'required_fields' in validators:
                    importer.validate_required_fields(validators['required_fields'])

                if 'unique_fields' in validators:
                    importer.validate_unique_fields(validators['unique_fields'])

            # 如果有错误，返回验证摘要
            if importer.errors:
                return importer.get_validation_summary()

            # 清理数据
            df = importer.clean_data()

            # 开始计时
            start_time = time.time()

            # 创建导入历史记录
            import_history = ImportHistory.objects.create(
                user_id=self.request.current_staff.id,
                import_type='staff',
                filename=file.name,
                status='processing'
            )

            # 导入数据
            success_count, error_count, errors = self._import_staff_to_database(df)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 更新导入历史
            import_history.success_count = success_count
            import_history.error_count = error_count
            import_history.total_count = len(df)
            import_history.processing_time = processing_time
            import_history.status = 'completed' if error_count == 0 else ('partial' if success_count > 0 else 'failed')
            if errors:
                import_history.error_details = {'errors': errors}
            import_history.save()

            return {
                'success': True,
                'total_rows': len(df),
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors,
                'message': f'导入完成: 成功{success_count}条，失败{error_count}条'
            }

        except Exception as e:
            logger.error(f"员工导入处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '导入处理失败，请检查文件格式'
            }

    def _import_staff_to_database(self, df):
        """导入员工数据到数据库"""
        import pandas as pd
        import uuid
        import hashlib
        from django.utils import timezone

        success_count = 0
        error_count = 0
        errors = []

        for idx, row in df.iterrows():
            try:
                # 获取基本字段
                name = str(row['姓名*']).strip() if pd.notna(row['姓名*']) else None
                department_name = str(row['部门*']).strip() if pd.notna(row['部门*']) else None
                level = int(row['级别*']) if pd.notna(row['级别*']) else None
                employee_no = str(row['工号']).strip() if pd.notna(row['工号']) and str(row['工号']).strip() else None
                position_name = str(row['职位']).strip() if pd.notna(row['职位']) and str(row['职位']).strip() else None
                email = str(row['邮箱']).strip() if pd.notna(row['邮箱']) and str(row['邮箱']).strip() else ''
                phone = str(row['手机号']).strip() if pd.notna(row['手机号']) and str(row['手机号']).strip() else ''

                # 验证必填字段
                if not name or not department_name or not level:
                    error_count += 1
                    errors.append(f"第{idx+2}行：姓名、部门和级别为必填项")
                    continue

                # 验证级别范围
                if level < 1 or level > 9:
                    error_count += 1
                    errors.append(f"第{idx+2}行：级别必须在1-9之间")
                    continue

                # 查找部门
                try:
                    department = Department.objects.get(
                        name=department_name,
                        deleted_at__isnull=True
                    )
                except Department.DoesNotExist:
                    error_count += 1
                    errors.append(f"第{idx+2}行：找不到部门'{department_name}'，请确保部门已存在")
                    continue
                except Department.MultipleObjectsReturned:
                    error_count += 1
                    errors.append(f"第{idx+2}行：存在多个名为'{department_name}'的部门，请检查数据")
                    continue

                # 查找职位（如果提供）
                position = None
                if position_name:
                    try:
                        position = Position.objects.get(
                            name=position_name,
                            department=department,
                            deleted_at__isnull=True
                        )
                    except Position.DoesNotExist:
                        error_count += 1
                        errors.append(f"第{idx+2}行：在部门'{department_name}'中找不到职位'{position_name}'，请确保职位已存在")
                        continue
                    except Position.MultipleObjectsReturned:
                        error_count += 1
                        errors.append(f"第{idx+2}行：在部门'{department_name}'中存在多个名为'{position_name}'的职位，请检查数据")
                        continue

                # 生成员工编号（如果未提供）
                if not employee_no:
                    # 生成格式：EMP + 年月日 + 4位随机数
                    today = timezone.now().strftime('%Y%m%d')
                    random_suffix = str(uuid.uuid4().int)[:4]
                    employee_no = f"EMP{today}{random_suffix}"

                    # 确保员工编号唯一
                    while Staff.objects.filter(employee_no=employee_no, deleted_at__isnull=True).exists():
                        random_suffix = str(uuid.uuid4().int)[:4]
                        employee_no = f"EMP{today}{random_suffix}"

                # 检查员工编号是否已存在
                if Staff.objects.filter(employee_no=employee_no, deleted_at__isnull=True).exists():
                    error_count += 1
                    errors.append(f"第{idx+2}行：员工编号'{employee_no}'已存在")
                    continue

                # 生成用户名（基于姓名拼音或员工编号）
                username = employee_no.lower()

                # 确保用户名唯一
                original_username = username
                counter = 1
                while Staff.objects.filter(username=username, deleted_at__isnull=True).exists():
                    username = f"{original_username}{counter}"
                    counter += 1

                # 生成匿名编号
                anonymous_code = f"A{str(uuid.uuid4().int)[:7]}"
                while Staff.objects.filter(anonymous_code=anonymous_code, deleted_at__isnull=True).exists():
                    anonymous_code = f"A{str(uuid.uuid4().int)[:7]}"

                # 生成默认密码（员工编号）
                default_password = hashlib.sha256(employee_no.encode()).hexdigest()

                # 根据级别确定角色
                if level >= 8:
                    role = 'dept_manager'  # 8级以上为部门经理
                elif level >= 6:
                    role = 'admin'  # 6-7级为普通管理员
                else:
                    role = 'employee'  # 1-5级为普通员工

                # 准备员工数据
                staff_data = {
                    'username': username,
                    'password': default_password,
                    'anonymous_code': anonymous_code,
                    'employee_no': employee_no,
                    'name': name,
                    'department': department,
                    'position': position,
                    'email': email,
                    'phone': phone,
                    'role': role,
                    'is_active': True,
                    'created_by': self.request.current_staff,
                    'force_password_change': True,  # 强制首次登录修改密码
                }

                # 创建员工
                staff = Staff.objects.create(**staff_data)
                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"第{idx+2}行：导入失败 - {str(e)}")
                logger.error(f"导入第{idx+2}行员工数据失败: {e}")

        return success_count, error_count, errors


class StaffExportView(View):
    """员工信息导出视图"""
    
    def get(self, request):
        """导出员工信息"""
        try:
            # 获取筛选参数
            department_id = request.GET.get('department')
            role = request.GET.get('role')
            is_active = request.GET.get('is_active')
            
            # 构建查询集
            queryset = Staff.objects.select_related('department', 'position')
            
            if department_id:
                queryset = queryset.filter(department_id=department_id)
            if role:
                queryset = queryset.filter(role=role)
            if is_active:
                queryset = queryset.filter(is_active=is_active == 'true')
            
            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)
            
            # 定义字段映射
            field_mapping = {
                'employee_no': '员工编号',
                'name': '姓名',
                'department.name': '所属部门',
                'position.name': '职位',
                'email': '邮箱',
                'phone': '手机号',
                'username': '用户名',
                'anonymous_code': '匿名编号',
                'role': '角色',
                'is_active': '是否激活',
                'created_at': '创建时间',
                'last_login': '最后登录'
            }
            
            # 生成文件名
            filename = f'员工信息导出_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            # 导出数据
            response = processor.export_data(
                queryset, 'staff', field_mapping, filename
            )
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='export',
                target_model='Staff',
                description=f'导出员工信息: {queryset.count()}条记录',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return response
            
        except Exception as e:
            logger.error(f"员工信息导出失败: {e}")
            messages.error(request, f'导出失败: {str(e)}')
            return redirect('organizations:admin:staff_list')


class DepartmentImportView(View):
    """部门信息导入视图"""
    template_name = 'admin/department/import.html'

    @method_decorator(require_permission(Permission.ORG_CREATE_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """显示导入页面"""
        recent_imports = ImportHistory.objects.filter(
            import_type='department',
            user_id=request.current_staff.id
        )[:10]
        
        context = {
            'recent_imports': recent_imports,
        }
        return render(request, self.template_name, context)
    
    def post(self, request):
        """处理导入请求"""
        try:
            if 'file' not in request.FILES:
                return JsonResponse({'success': False, 'error': '请选择要导入的文件'})
            
            file = request.FILES['file']
            
            if not file.name.endswith(('.xlsx', '.xls')):
                return JsonResponse({'success': False, 'error': '只支持Excel文件格式'})
            
            processor = ExcelProcessor(request.current_staff.id)
            
            field_mapping = {
                '部门编号*': 'dept_code',
                '部门名称*': 'name',
                '上级部门名称': 'parent_department__name',
                '备注': 'description'
            }
            
            validators = {
                'required_fields': ['部门编号*', '部门名称*'],
                'unique_fields': ['部门编号*'],
                'data_types': {
                    '部门编号*': 'string',
                    '部门名称*': 'string',
                    '备注': 'string'
                }
            }
            
            # 使用自定义的部门导入逻辑
            result = self._process_department_import(file, field_mapping, validators)

            return JsonResponse(result)
            
        except Exception as e:
            logger.error(f"部门信息导入失败: {e}")
            return JsonResponse({'success': False, 'error': str(e)})

    def _process_department_import(self, file, field_mapping, validators):
        """自定义部门导入处理逻辑"""
        import pandas as pd
        import time
        from io import BytesIO
        from common.models import ImportHistory

        try:
            # 将Django UploadedFile转换为BytesIO
            file_buffer = BytesIO(file.read())

            # 创建导入器
            importer = ExcelImporter(file_buffer)

            # 读取Excel
            df = importer.read_excel()

            # 验证表头
            required_headers = list(field_mapping.keys())
            if not importer.validate_headers(required_headers):
                return importer.get_validation_summary()

            # 数据验证
            if validators:
                if 'data_types' in validators:
                    importer.validate_data_types(validators['data_types'])

                if 'required_fields' in validators:
                    importer.validate_required_fields(validators['required_fields'])

                if 'unique_fields' in validators:
                    importer.validate_unique_fields(validators['unique_fields'])

            # 如果有错误，返回验证摘要
            if importer.errors:
                return importer.get_validation_summary()

            # 清理数据
            df = importer.clean_data()

            # 开始计时
            start_time = time.time()

            # 创建导入历史记录
            import_history = ImportHistory.objects.create(
                user_id=self.request.current_staff.id,
                import_type='department',
                filename=file.name,
                status='processing'
            )

            # 导入数据
            success_count, error_count, errors = self._import_departments_to_database(df)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 更新导入历史
            import_history.success_count = success_count
            import_history.error_count = error_count
            import_history.total_count = len(df)
            import_history.processing_time = processing_time
            import_history.status = 'completed' if error_count == 0 else ('partial' if success_count > 0 else 'failed')
            if errors:
                import_history.error_details = {'errors': errors}
            import_history.save()

            return {
                'success': True,
                'total_rows': len(df),
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors,
                'message': f'导入完成: 成功{success_count}条，失败{error_count}条'
            }

        except Exception as e:
            logger.error(f"部门导入处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '导入处理失败，请检查文件格式'
            }

    def _import_departments_to_database(self, df):
        """导入部门数据到数据库"""
        import pandas as pd

        success_count = 0
        error_count = 0
        errors = []

        # 按层级排序，先处理没有上级部门的（顶级部门）
        # 然后按上级部门名称排序，确保上级部门先被创建
        df_sorted = df.copy()

        # 安全地检查上级部门名称是否存在
        # 先将上级部门名称列转换为字符串，然后检查是否为空
        df_sorted['上级部门名称'] = df_sorted['上级部门名称'].astype(str)
        df_sorted['has_parent'] = (
            df_sorted['上级部门名称'].notna() &
            (df_sorted['上级部门名称'] != 'nan') &
            (df_sorted['上级部门名称'].str.strip() != '')
        )
        df_sorted = df_sorted.sort_values(['has_parent', '上级部门名称'])

        for idx, row in df_sorted.iterrows():
            try:
                # 获取基本字段
                dept_code = str(row['部门编号*']).strip() if pd.notna(row['部门编号*']) else None
                dept_name = str(row['部门名称*']).strip() if pd.notna(row['部门名称*']) else None

                # 处理上级部门名称（已经转换为字符串）
                parent_name = None
                if row['上级部门名称'] and row['上级部门名称'] != 'nan':
                    parent_name_str = str(row['上级部门名称']).strip()
                    if parent_name_str:
                        parent_name = parent_name_str

                remark = str(row['备注']).strip() if pd.notna(row['备注']) and str(row['备注']).strip() != 'nan' else ''

                # 验证必填字段
                if not dept_code or not dept_name:
                    error_count += 1
                    errors.append(f"第{idx+2}行：部门编号和部门名称为必填项")
                    continue

                # 查找上级部门
                parent_department = None
                if parent_name:
                    try:
                        parent_department = Department.objects.get(
                            name=parent_name,
                            deleted_at__isnull=True
                        )
                    except Department.DoesNotExist:
                        error_count += 1
                        errors.append(f"第{idx+2}行：找不到上级部门'{parent_name}'，请确保上级部门已存在")
                        continue
                    except Department.MultipleObjectsReturned:
                        error_count += 1
                        errors.append(f"第{idx+2}行：存在多个名为'{parent_name}'的部门，请检查数据")
                        continue

                # 准备部门数据
                dept_data = {
                    'name': dept_name,
                    'parent_department': parent_department,
                    'description': remark,
                    'created_by': self.request.current_staff,
                }

                # 创建或更新部门
                department, created = Department.objects.get_or_create(
                    dept_code=dept_code,
                    defaults=dept_data
                )

                if not created:
                    # 更新现有部门
                    for key, value in dept_data.items():
                        if key != 'created_by':  # 不更新创建者
                            setattr(department, key, value)
                    department.updated_by = self.request.current_staff
                    department.save()

                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"第{idx+2}行：导入失败 - {str(e)}")
                logger.error(f"导入第{idx+2}行部门数据失败: {e}")

        return success_count, error_count, errors


class DepartmentExportView(View):
    """部门信息导出视图"""

    @method_decorator(require_permission(Permission.ORG_VIEW_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """导出部门信息"""
        try:
            queryset = Department.objects.select_related('parent_department', 'manager')
            
            processor = ExcelProcessor(request.current_staff.id)
            
            field_mapping = {
                'dept_code': '部门编号',
                'name': '部门名称',
                'parent_department.name': '上级部门',
                'manager.name': '部门经理',
                'location': '办公地址',
                'phone': '联系电话',
                'email': '邮箱地址',
                'description': '部门描述',
                'created_at': '创建时间'
            }
            
            filename = f'部门信息导出_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            return processor.export_data(queryset, 'department', field_mapping, filename)
            
        except Exception as e:
            logger.error(f"部门信息导出失败: {e}")
            messages.error(request, f'导出失败: {str(e)}')
            return redirect('organizations:admin:department_list')


class PositionImportView(View):
    """职位信息导入视图"""
    template_name = 'admin/position/import.html'

    @method_decorator(require_permission(Permission.ORG_CREATE_POSITION))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        """显示导入页面"""
        recent_imports = ImportHistory.objects.filter(
            import_type='position',
            user_id=request.current_staff.id
        )[:10]

        context = {
            'recent_imports': recent_imports,
        }
        return render(request, self.template_name, context)

    def post(self, request):
        """处理导入请求"""
        try:
            if 'file' not in request.FILES:
                return JsonResponse({'success': False, 'error': '请选择要导入的文件'})

            file = request.FILES['file']

            if not file.name.endswith(('.xlsx', '.xls')):
                return JsonResponse({'success': False, 'error': '只支持Excel文件格式'})

            processor = ExcelProcessor(request.current_staff.id)

            field_mapping = {
                '部门*': 'department__name',
                '职位*': 'name',
                '编码*': 'position_code',
                '是否管理岗': 'is_management',
                '是否部门主管': 'is_department_manager',
                '职位级别': 'level',
                '备注': 'description'
            }

            validators = {
                'required_fields': ['部门*', '职位*', '编码*'],
                'unique_fields': ['编码*'],
                'data_types': {
                    '部门*': 'string',
                    '职位*': 'string',
                    '编码*': 'string',
                    '是否管理岗': 'boolean',
                    '是否部门主管': 'boolean',
                    '职位级别': 'integer',
                    '备注': 'string'
                }
            }

            # 使用自定义的职位导入逻辑
            result = self._process_position_import(file, field_mapping, validators)

            return JsonResponse(result)

        except Exception as e:
            logger.error(f"职位信息导入失败: {e}")
            return JsonResponse({'success': False, 'error': str(e)})

    def _process_position_import(self, file, field_mapping, validators):
        """自定义职位导入处理逻辑"""
        import pandas as pd
        import time
        from io import BytesIO
        from common.models import ImportHistory

        try:
            # 读取Excel文件
            importer = ExcelImporter(BytesIO(file.read()))
            df = importer.read_excel()

            # 验证数据
            validation_result = importer.validate_data(field_mapping, validators)
            if not validation_result['is_valid']:
                return {
                    'success': False,
                    'error': '数据验证失败',
                    'errors': validation_result['errors'],
                    'message': '请检查数据格式和必填字段'
                }

            # 清理数据
            df = importer.clean_data()

            # 开始计时
            start_time = time.time()

            # 创建导入历史记录
            import_history = ImportHistory.objects.create(
                user_id=self.request.current_staff.id,
                import_type='position',
                filename=file.name,
                status='processing'
            )

            # 导入数据
            success_count, error_count, errors = self._import_positions_to_database(df)

            # 计算处理时间
            processing_time = time.time() - start_time

            # 更新导入历史
            import_history.success_count = success_count
            import_history.error_count = error_count
            import_history.total_count = len(df)
            import_history.processing_time = processing_time
            import_history.status = 'completed' if error_count == 0 else ('partial' if success_count > 0 else 'failed')
            if errors:
                import_history.error_details = {'errors': errors}
            import_history.save()

            return {
                'success': True,
                'total_rows': len(df),
                'success_count': success_count,
                'error_count': error_count,
                'errors': errors,
                'message': f'导入完成: 成功{success_count}条，失败{error_count}条'
            }

        except Exception as e:
            logger.error(f"职位导入处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'message': '导入处理失败，请检查文件格式'
            }

    def _import_positions_to_database(self, df):
        """导入职位数据到数据库"""
        import pandas as pd

        success_count = 0
        error_count = 0
        errors = []

        for idx, row in df.iterrows():
            try:
                # 获取部门
                department_name = str(row['部门*']).strip()
                try:
                    department = Department.objects.get(name=department_name, deleted_at__isnull=True)
                except Department.DoesNotExist:
                    error_count += 1
                    errors.append(f"第{idx+2}行：找不到部门 '{department_name}'")
                    continue

                # 准备职位数据
                position_data = {
                    'department': department,
                    'name': str(row['职位*']).strip(),
                    'position_code': str(row['编码*']).strip(),
                    'created_by': self.request.current_staff,
                    'updated_by': self.request.current_staff,
                }

                # 处理可选字段
                if pd.notna(row.get('是否管理岗')):
                    is_management_str = str(row['是否管理岗']).strip()
                    position_data['is_management'] = is_management_str in ['是', 'True', '1', 'true', 'YES', 'yes']

                if pd.notna(row.get('是否部门主管')):
                    is_dept_manager_str = str(row['是否部门主管']).strip()
                    position_data['is_department_manager'] = is_dept_manager_str in ['是', 'True', '1', 'true', 'YES', 'yes']

                if pd.notna(row.get('职位级别')):
                    try:
                        level = int(float(row['职位级别']))
                        if 1 <= level <= 9:
                            position_data['level'] = level
                        else:
                            errors.append(f"第{idx+2}行：职位级别必须在1-9之间")
                            error_count += 1
                            continue
                    except (ValueError, TypeError):
                        errors.append(f"第{idx+2}行：职位级别格式错误")
                        error_count += 1
                        continue

                if pd.notna(row.get('备注')):
                    position_data['description'] = str(row['备注']).strip()

                # 检查职位编码在部门内的唯一性
                existing_position = Position.objects.filter(
                    department=department,
                    position_code=position_data['position_code'],
                    deleted_at__isnull=True
                ).first()

                if existing_position:
                    # 更新现有职位
                    for key, value in position_data.items():
                        if key not in ['created_by', 'department']:  # 不更新创建者和部门
                            setattr(existing_position, key, value)
                    existing_position.save()
                else:
                    # 创建新职位
                    Position.objects.create(**position_data)

                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"第{idx+2}行：导入失败 - {str(e)}")
                logger.error(f"导入第{idx+2}行职位数据失败: {e}")

        return success_count, error_count, errors


class PositionExportView(View):
    """职位信息导出视图"""
    
    def get(self, request):
        """导出职位信息"""
        try:
            # 获取筛选参数
            department_id = request.GET.get('department')
            level = request.GET.get('level')
            is_management = request.GET.get('is_management')
            
            # 构建查询集
            queryset = Position.objects.select_related('department').filter(
                deleted_at__isnull=True
            ).annotate(
                staff_count=Count('staff', filter=Q(staff__is_active=True))
            )
            
            # 应用筛选条件
            if department_id:
                queryset = queryset.filter(department_id=department_id)
            if level:
                level_range = level.split('-')
                if len(level_range) == 2:
                    queryset = queryset.filter(
                        level__gte=level_range[0], 
                        level__lte=level_range[1]
                    )
                else:
                    queryset = queryset.filter(level=level)
            if is_management:
                queryset = queryset.filter(is_management=is_management == 'true')
                
            # 添加搜索功能
            search = request.GET.get('search')
            if search:
                queryset = queryset.filter(
                    Q(name__icontains=search) | 
                    Q(position_code__icontains=search) |
                    Q(department__name__icontains=search)
                )
            
            # 创建Excel处理器
            processor = ExcelProcessor(request.current_staff.id)
            
            # 定义字段映射
            field_mapping = {
                'position_code': '职位编码',
                'name': '职位名称',
                'department.name': '所属部门',
                'level': '职位级别',
                'get_level_display_full': '级别名称',
                'is_management': '是否管理岗',
                'is_department_manager': '是否部门经理',
                'description': '职位描述',
                'created_at': '创建时间',
                'staff_count': '在职人数',
                'is_active': '是否启用'
            }
            
            # 生成文件名
            filename = f'职位信息导出_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            
            # 导出数据
            response = processor.export_data(
                queryset, 'position', field_mapping, filename
            )
            
            # 记录审计日志
            AuditLog.objects.create(
                user=request.current_staff.name,
                action='export',
                target_model='Position',
                description=f'导出职位信息: {queryset.count()}条记录',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            
            return response
            
        except Exception as e:
            logger.error(f"职位信息导出失败: {e}")
            messages.error(request, f'导出失败: {str(e)}')
            return redirect('organizations:admin:position_list')


class ExcelTemplateDownloadView(View):
    """Excel模板下载视图"""

    @method_decorator(require_permission(Permission.ORG_VIEW_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get(self, request, template_type):
        """下载对应类型的模板"""
        try:
            if template_type == 'staff':
                return ExcelTemplateGenerator.generate_staff_template()
            elif template_type == 'department':
                return ExcelTemplateGenerator.generate_department_template()
            elif template_type == 'position':
                return ExcelTemplateGenerator.generate_position_template()
            elif template_type == 'evaluation_relation':
                return ExcelTemplateGenerator.generate_evaluation_relation_template()
            else:
                return JsonResponse({'error': '不支持的模板类型'}, status=400)
                
        except Exception as e:
            logger.error(f"模板下载失败: {e}")
            return JsonResponse({'error': '模板生成失败'}, status=500)


class ImportHistoryView(ListView):
    """导入历史记录视图"""
    model = ImportHistory
    template_name = 'admin/common/import_history.html'
    context_object_name = 'import_records'
    paginate_by = 20

    @method_decorator(require_permission(Permission.ORG_VIEW_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """获取当前用户的导入历史"""
        queryset = ImportHistory.objects.filter(
            user_id=self.request.current_staff.id
        ).order_by('-created_at')
        
        # 筛选参数
        import_type = self.request.GET.get('type')
        status = self.request.GET.get('status')
        
        if import_type:
            queryset = queryset.filter(import_type=import_type)
        if status:
            queryset = queryset.filter(status=status)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        """添加额外的上下文数据"""
        context = super().get_context_data(**kwargs)
        
        context['import_types'] = ImportHistory.IMPORT_TYPES
        context['status_choices'] = ImportHistory.STATUS_CHOICES
        context['selected_type'] = self.request.GET.get('type', '')
        context['selected_status'] = self.request.GET.get('status', '')
        
        return context


class GenerateAnonymousCodeView(View):
    """
    生成安全匿名编号API
    支持为新员工或现有员工生成安全编号
    """

    def dispatch(self, request, *args, **kwargs):
        # 检查用户认证和权限
        if not getattr(request, 'is_authenticated', False) or not request.current_staff:
            return JsonResponse({'success': False, 'error': '未认证'}, status=401)

        if not request.current_staff.is_manager:
            return JsonResponse({'success': False, 'error': '权限不足'}, status=403)

        return super().dispatch(request, *args, **kwargs)

    def post(self, request):
        """生成安全匿名编号"""
        try:
            import json
            from common.security.anonymous import SecureAnonymousCodeGenerator

            data = json.loads(request.body)
            staff_id = data.get('staff_id')  # 可选，用于更新现有员工
            department_id = data.get('department_id', 1)  # 部门ID，默认为1

            # 生成安全编号
            generator = SecureAnonymousCodeGenerator()

            # 确保编号唯一性
            max_attempts = 10
            for attempt in range(max_attempts):
                # 为新员工生成临时ID用于编号生成
                temp_staff_id = staff_id if staff_id else int(time.time() * 1000) % 1000000

                new_code = generator.generate_secure_code(temp_staff_id, department_id)

                # 检查编号是否已存在
                existing_staff = Staff.objects.filter(
                    models.Q(new_anonymous_code=new_code) | models.Q(anonymous_code=new_code),
                    deleted_at__isnull=True
                ).exclude(id=staff_id if staff_id else None).first()

                if not existing_staff:
                    # 编号唯一，可以使用
                    break

                if attempt == max_attempts - 1:
                    return JsonResponse({
                        'success': False,
                        'error': '生成唯一编号失败，请重试'
                    }, status=500)
            
            # 如果提供了staff_id，更新数据库
            if staff_id:
                try:
                    staff = Staff.objects.get(id=staff_id, deleted_at__isnull=True)
                    old_code = staff.new_anonymous_code or staff.anonymous_code
                    
                    # 更新为新的安全编号
                    staff.new_anonymous_code = new_code
                    staff.save(update_fields=['new_anonymous_code'])
                    
                    # 记录审计日志
                    AuditLog.objects.create(
                        user_id=request.current_staff.id,
                        action='更新员工匿名编号',
                        resource_type='Staff',
                        resource_id=staff.id,
                        details=f'员工 {staff.name} 的匿名编号从 {old_code} 更新为安全编号'
                    )
                    
                    logger.info(f"管理员 {request.current_staff.username} 为员工 {staff.name} 重新生成了安全匿名编号")
                    
                except Staff.DoesNotExist:
                    return JsonResponse({
                        'success': False, 
                        'error': '员工不存在'
                    }, status=404)
            
            return JsonResponse({
                'success': True,
                'anonymous_code': new_code,
                'message': '安全编号生成成功'
            })
            
        except json.JSONDecodeError:
            return JsonResponse({
                'success': False, 
                'error': '请求数据格式错误'
            }, status=400)
        except Exception as e:
            logger.error(f"生成匿名编号失败: {e}")
            return JsonResponse({
                'success': False, 
                'error': '生成编号失败，请重试'
            }, status=500)


class ProfileManagementView(View):
    """
    个人资料管理视图
    允许用户查看和修改个人信息
    """
    template_name = 'admin/profile/management.html'
    
    def dispatch(self, request, *args, **kwargs):
        # 检查用户是否已认证
        if not getattr(request, 'is_authenticated', False) or not request.current_staff:
            return redirect('organizations:admin:login')
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """显示个人资料页面"""
        staff = request.current_staff
        
        context = {
            'staff': staff,
            'departments': Department.objects.filter(deleted_at__isnull=True, is_active=True),
            'positions': Position.objects.filter(deleted_at__isnull=True, is_active=True),
            'role_choices': Staff.ROLE_CHOICES,
        }
        return render(request, self.template_name, context)
        
    def post(self, request):
        """处理个人资料更新"""
        staff = request.current_staff
        
        try:
            with transaction.atomic():
                # 更新基本信息
                staff.name = request.POST.get('name', '').strip()
                staff.email = request.POST.get('email', '').strip()
                staff.phone = request.POST.get('phone', '').strip()
                
                # 只有超级管理员可以修改角色
                if staff.is_super_admin:
                    new_role = request.POST.get('role')
                    if new_role and new_role in dict(Staff.ROLE_CHOICES):
                        staff.role = new_role
                
                staff.save()
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=staff.username,
                    action='update',
                    target_model='Staff',
                    target_id=staff.id,
                    description=f'{staff.name} 更新个人资料',
                    ip_address=self._get_client_ip(request)
                )
                
                messages.success(request, '个人资料更新成功！')
                
        except Exception as e:
            logger.error(f"个人资料更新失败: {e}")
            messages.error(request, '个人资料更新失败，请重试')
        
        return redirect('organizations:admin:profile_management')
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip


class ChangePasswordView(View):
    """
    修改密码视图
    """
    template_name = 'admin/profile/change_password.html'
    
    def dispatch(self, request, *args, **kwargs):
        # 检查用户是否已认证
        if not getattr(request, 'is_authenticated', False) or not request.current_staff:
            return redirect('organizations:admin:login')
        return super().dispatch(request, *args, **kwargs)
    
    def get(self, request):
        """显示修改密码页面"""
        return render(request, self.template_name)
        
    def post(self, request):
        """处理密码修改"""
        staff = request.current_staff
        
        current_password = request.POST.get('current_password', '')
        new_password = request.POST.get('new_password', '')
        confirm_password = request.POST.get('confirm_password', '')
        
        # 验证当前密码
        if not staff.check_password(current_password):
            messages.error(request, '当前密码错误')
            return render(request, self.template_name)
        
        # 验证新密码
        if len(new_password) < 6:
            messages.error(request, '新密码长度不能少于6位')
            return render(request, self.template_name)
        
        if new_password != confirm_password:
            messages.error(request, '两次输入的新密码不一致')
            return render(request, self.template_name)
        
        try:
            # 更新密码
            staff.update_password(new_password)
            
            # 记录审计日志
            AuditLog.objects.create(
                user=staff.username,
                action='update_password',
                target_model='Staff',
                target_id=staff.id,
                description=f'{staff.name} 修改登录密码',
                ip_address=self._get_client_ip(request)
            )
            
            messages.success(request, '密码修改成功！')
            return redirect('organizations:admin:profile_management')
            
        except Exception as e:
            logger.error(f"密码修改失败: {e}")
            messages.error(request, '密码修改失败，请重试')
            return render(request, self.template_name)
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip



# ===== 批量操作视图 =====

class StaffBatchStatusView(View):
    """批量更改员工状态视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            staff_ids = data.get("staff_ids", [])
            is_active = data.get("is_active", True)
            
            if not staff_ids:
                return JsonResponse({"success": False, "message": "未选择任何员工"})
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                return JsonResponse({"success": False, "message": "用户未认证"})
            
            # 检查权限
            if not current_staff.has_permission("staff_management"):
                return JsonResponse({"success": False, "message": "权限不足"})
            
            with transaction.atomic():
                # 获取要更新的员工
                staff_list = Staff.objects.filter(id__in=staff_ids)
                
                # 防止操作超级管理员
                super_admins = staff_list.filter(role="super_admin")
                if super_admins.exists() and current_staff.role != "super_admin":
                    return JsonResponse({"success": False, "message": "无权操作超级管理员"})
                
                # 防止自己禁用自己
                if not is_active and str(current_staff.id) in [str(id) for id in staff_ids]:
                    return JsonResponse({"success": False, "message": "不能禁用自己的账户"})
                
                # 批量更新状态
                updated_count = staff_list.update(
                    is_active=is_active,
                    updated_at=timezone.now()
                )
                
                # 记录操作日志
                action = "启用" if is_active else "禁用"
                AuditLog.objects.create(
                    user=current_staff.username,
                    action="update",
                    target_model="Staff",
                    description=f"批量{action} {updated_count} 个员工",
                    extra_data={
                        "staff_ids": staff_ids,
                        "is_active": is_active,
                        "operator": current_staff.name,
                        "batch_operation": True,
                        "action_type": "status_change"
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                
                return JsonResponse({
                    "success": True,
                    "updated_count": updated_count,
                    "message": f"成功{action} {updated_count} 个员工"
                })
                
        except Exception as e:
            logger.error(f"批量更改员工状态失败: {e}")
            return JsonResponse({"success": False, "message": "操作失败，请重试"})
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class StaffBatchDeleteView(View):
    """批量删除员工视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            staff_ids = data.get("staff_ids", [])
            
            if not staff_ids:
                return JsonResponse({"success": False, "message": "未选择任何员工"})
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                return JsonResponse({"success": False, "message": "用户未认证"})
            
            # 检查权限
            if not current_staff.has_permission("staff_management"):
                return JsonResponse({"success": False, "message": "权限不足"})
            
            with transaction.atomic():
                # 获取要删除的员工
                staff_list = Staff.objects.filter(id__in=staff_ids)
                
                # 防止删除超级管理员
                super_admins = staff_list.filter(role="super_admin")
                if super_admins.exists() and current_staff.role != "super_admin":
                    return JsonResponse({"success": False, "message": "无权删除超级管理员"})
                
                # 防止删除自己
                if str(current_staff.id) in [str(id) for id in staff_ids]:
                    return JsonResponse({"success": False, "message": "不能删除自己的账户"})
                
                # 记录要删除的员工名称（在删除前获取）
                staff_names = list(staff_list.values_list("name", flat=True))
                
                # 软删除员工
                deleted_count = 0
                for staff in staff_list:
                    staff.soft_delete(deleted_by=current_staff.username)
                    deleted_count += 1
                
                # 记录操作日志
                AuditLog.objects.create(
                    user=current_staff.username,
                    action="delete",
                    target_model="Staff",
                    description=f"批量删除 {deleted_count} 个员工",
                    extra_data={
                        "staff_ids": staff_ids,
                        "staff_names": staff_names,
                        "operator": current_staff.name,
                        "batch_operation": True
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                
                return JsonResponse({
                    "success": True,
                    "deleted_count": deleted_count,
                    "message": f"成功删除 {deleted_count} 个员工"
                })
                
        except Exception as e:
            logger.error(f"批量删除员工失败: {e}")
            return JsonResponse({"success": False, "message": "删除失败，请重试"})
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class StaffBatchDepartmentView(View):
    """批量分配部门视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            staff_ids = data.get("staff_ids", [])
            department_id = data.get("department_id")
            
            if not staff_ids:
                return JsonResponse({"success": False, "message": "未选择任何员工"})
            
            if not department_id:
                return JsonResponse({"success": False, "message": "未选择目标部门"})
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                return JsonResponse({"success": False, "message": "用户未认证"})
            
            # 检查权限
            if not current_staff.has_permission("staff_management"):
                return JsonResponse({"success": False, "message": "权限不足"})
            
            # 验证部门存在
            try:
                department = Department.objects.get(id=department_id, deleted_at__isnull=True)
            except Department.DoesNotExist:
                return JsonResponse({"success": False, "message": "目标部门不存在"})
            
            with transaction.atomic():
                # 获取要更新的员工
                staff_list = Staff.objects.filter(id__in=staff_ids, deleted_at__isnull=True)
                
                # 批量更新部门
                updated_count = staff_list.update(
                    department=department,
                    updated_at=timezone.now()
                )
                
                # 记录操作日志
                staff_names = list(staff_list.values_list("name", flat=True))
                AuditLog.objects.create(
                    user=current_staff.username,
                    action="batch_assign_department",
                    target_model="Staff",
                    description=f"批量分配 {updated_count} 个员工到部门: {department.name}",
                    changes=json.dumps({
                        "staff_ids": staff_ids,
                        "staff_names": staff_names,
                        "department_id": department_id,
                        "department_name": department.name,
                        "operator": current_staff.name
                    }),
                    ip_address=self._get_client_ip(request)
                )
                
                return JsonResponse({
                    "success": True,
                    "updated_count": updated_count,
                    "message": f"成功分配 {updated_count} 个员工到 {department.name}"
                })
                
        except Exception as e:
            logger.error(f"批量分配部门失败: {e}")
            return JsonResponse({"success": False, "message": "分配失败，请重试"})
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class StaffBatchRoleView(View):
    """批量分配角色视图"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            staff_ids = data.get("staff_ids", [])
            role = data.get("role")
            
            if not staff_ids:
                return JsonResponse({"success": False, "message": "未选择任何员工"})
            
            if not role:
                return JsonResponse({"success": False, "message": "未选择目标角色"})
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                return JsonResponse({"success": False, "message": "用户未认证"})
            
            # 检查权限
            if not current_staff.has_permission("staff_management"):
                return JsonResponse({"success": False, "message": "权限不足"})
            
            # 验证角色有效性
            valid_roles = ["employee", "admin", "dept_manager", "eval_admin", "hr_admin", "system_admin", "super_admin"]
            if role not in valid_roles:
                return JsonResponse({"success": False, "message": "无效的角色"})
            
            # 非超级管理员不能分配超级管理员权限
            if role == "super_admin" and current_staff.role != "super_admin":
                return JsonResponse({"success": False, "message": "无权分配超级管理员角色"})
            
            with transaction.atomic():
                # 获取要更新的员工
                staff_list = Staff.objects.filter(id__in=staff_ids, deleted_at__isnull=True)
                
                # 防止修改超级管理员的角色（除非操作者也是超级管理员）
                if current_staff.role != "super_admin":
                    super_admins = staff_list.filter(role="super_admin")
                    if super_admins.exists():
                        return JsonResponse({"success": False, "message": "无权修改超级管理员的角色"})
                
                # 批量更新角色
                updated_count = staff_list.update(
                    role=role,
                    updated_at=timezone.now()
                )
                
                # 记录操作日志
                staff_names = list(staff_list.values_list("name", flat=True))
                role_display = dict(Staff.ROLE_CHOICES).get(role, role)
                
                AuditLog.objects.create(
                    user=current_staff.username,
                    action="batch_assign_role",
                    target_model="Staff",
                    description=f"批量分配 {updated_count} 个员工为角色: {role_display}",
                    changes=json.dumps({
                        "staff_ids": staff_ids,
                        "staff_names": staff_names,
                        "role": role,
                        "role_display": role_display,
                        "operator": current_staff.name
                    }),
                    ip_address=self._get_client_ip(request)
                )
                
                return JsonResponse({
                    "success": True,
                    "updated_count": updated_count,
                    "message": f"成功分配 {updated_count} 个员工为 {role_display}"
                })
                
        except Exception as e:
            logger.error(f"批量分配角色失败: {e}")
            return JsonResponse({"success": False, "message": "分配失败，请重试"})
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class StaffBatchExportView(View):
    """批量导出员工视图"""
    
    def post(self, request):
        try:
            staff_ids = request.POST.getlist("staff_ids")
            
            if not staff_ids:
                messages.error(request, "未选择任何员工")
                return redirect("organizations:admin:staff_list")
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                messages.error(request, "用户未认证")
                return redirect("organizations:admin:staff_list")
            
            # 检查权限
            if not current_staff.has_permission("staff_export"):
                messages.error(request, "权限不足")
                return redirect("organizations:admin:staff_list")
            
            # 获取员工数据
            staff_list = Staff.objects.filter(
                id__in=staff_ids, 
                deleted_at__isnull=True
            ).select_related("department", "position").order_by("employee_no")
            
            if not staff_list.exists():
                messages.error(request, "未找到有效的员工数据")
                return redirect("organizations:admin:staff_list")
            
            # 准备导出数据
            export_data = []
            for staff in staff_list:
                export_data.append({
                    "员工编号": staff.employee_no,
                    "姓名": staff.name,
                    "部门": staff.department.name if staff.department else "",
                    "职位": staff.position.name if staff.position else "",
                    "邮箱": staff.email or "",
                    "手机号": staff.phone or "",
                    "角色": staff.get_role_display(),
                    "状态": "在职" if staff.is_active else "离职",
                    "入职时间": staff.hire_date.strftime("%Y-%m-%d") if staff.hire_date else "",
                    "创建时间": staff.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "匿名编号": staff.new_anonymous_code or staff.anonymous_code or "",
                })
            
            # 创建Excel文件
            df = pd.DataFrame(export_data)
            
            # 生成文件响应
            response = HttpResponse(content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            response["Content-Disposition"] = f'attachment; filename="selected_staff_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
            
            # 写入Excel数据
            with pd.ExcelWriter(response, engine="openpyxl") as writer:
                df.to_excel(writer, sheet_name="员工信息", index=False)
                
                # 设置列宽
                worksheet = writer.sheets["员工信息"]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 记录操作日志
            AuditLog.objects.create(
                user=current_staff.username,
                action="batch_export",
                target_model="Staff",
                description=f"批量导出 {len(staff_list)} 个员工信息",
                changes=json.dumps({
                    "staff_ids": staff_ids,
                    "count": len(staff_list),
                    "operator": current_staff.name
                }),
                ip_address=self._get_client_ip(request)
            )
            
            return response
            
        except Exception as e:
            logger.error(f"批量导出员工失败: {e}")
            messages.error(request, "导出失败，请重试")
            return redirect("organizations:admin:staff_list")
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            ip = x_forwarded_for.split(",")[0].strip()
        else:
            ip = request.META.get("REMOTE_ADDR", "127.0.0.1")
        return ip


class DepartmentBatchDeleteView(View):
    """部门批量删除视图"""
    
    @method_decorator(require_permission(Permission.ORG_DELETE_DEPARTMENT))
    def dispatch(self, request, *args, **kwargs):
        return super().dispatch(request, *args, **kwargs)
    
    def post(self, request):
        """处理部门批量删除请求"""
        try:
            data = json.loads(request.body)
            department_ids = data.get("department_ids", [])
            
            if not department_ids:
                return JsonResponse({
                    'success': False,
                    'message': '未选择任何部门'
                })
            
            # 获取当前用户
            current_staff = request.current_staff
            if not current_staff:
                return JsonResponse({
                    'success': False,
                    'message': '用户未认证'
                })
            
            with transaction.atomic():
                # 获取要删除的部门
                departments = Department.objects.filter(
                    id__in=department_ids, 
                    deleted_at__isnull=True
                )
                
                if not departments.exists():
                    return JsonResponse({
                        'success': False,
                        'message': '没有找到可删除的部门'
                    })
                
                # 检查部门依赖关系
                dependencies = self._check_department_dependencies(departments)
                if dependencies:
                    return JsonResponse({
                        'success': False,
                        'message': f'无法删除这些部门，存在以下依赖关系：',
                        'dependencies': dependencies
                    })
                
                # 记录要删除的部门名称（在删除前获取）
                department_names = list(departments.values_list("name", flat=True))
                
                # 执行软删除
                deleted_count = 0
                for department in departments:
                    department.soft_delete(deleted_by=current_staff.username)
                    deleted_count += 1
                
                # 记录审计日志
                AuditLog.objects.create(
                    user=current_staff.username,
                    action="delete",
                    target_model="Department",
                    description=f"批量删除 {deleted_count} 个部门",
                    extra_data={
                        "department_ids": department_ids,
                        "department_names": department_names,
                        "operator": current_staff.name,
                        "batch_operation": True
                    },
                    ip_address=self._get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                
                return JsonResponse({
                    'success': True,
                    'message': f'成功删除 {deleted_count} 个部门',
                    'deleted_count': deleted_count
                })
                
        except Exception as e:
            logger.error(f"批量删除部门失败: {e}")
            return JsonResponse({
                'success': False,
                'message': f'删除失败: {str(e)}'
            })
    
    def _check_department_dependencies(self, departments):
        """检查部门删除前的依赖关系"""
        dependencies = []
        
        # 检查是否有员工属于这些部门
        for department in departments:
            staff_count = Staff.objects.filter(
                department=department,
                deleted_at__isnull=True
            ).count()
            
            if staff_count > 0:
                dependencies.append(f'{department.name}: 有 {staff_count} 个员工')
            
            # 检查是否有子部门
            child_count = Department.objects.filter(
                parent_department=department,
                deleted_at__isnull=True
            ).count()
            
            if child_count > 0:
                dependencies.append(f'{department.name}: 有 {child_count} 个子部门')
            
            # 检查是否有职位属于这些部门
            position_count = Position.objects.filter(
                department=department,
                deleted_at__isnull=True
            ).count()
            
            if position_count > 0:
                dependencies.append(f'{department.name}: 有 {position_count} 个职位')
        
        return dependencies
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip


class CheckboxTestView(TemplateView):
    """多选框测试页面"""
    template_name = 'admin/test_checkbox.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class StatsTestView(TemplateView):
    """统计数据测试页面"""
    template_name = 'admin/test_stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 员工统计
        staff_stats = Staff.objects.filter(deleted_at__isnull=True).aggregate(
            total_staff=Count('id'),
            active_staff=Count('id', filter=Q(is_active=True)),
            admin_staff=Count('id', filter=Q(role__in=['super_admin', 'system_admin', 'hr_admin', 'eval_admin', 'admin']))
        )

        # 职位统计
        position_stats = Position.objects.filter(deleted_at__isnull=True).aggregate(
            total_positions=Count('id'),
            management_count=Count('id', filter=Q(is_management=True)),
            department_manager_count=Count('id', filter=Q(is_department_manager=True)),
            max_level=models.Max('level'),
            departments_covered=Count('department', distinct=True)
        )

        # 部门统计
        department_stats = Department.objects.filter(deleted_at__isnull=True).aggregate(
            total_departments=Count('id'),
            active_departments=Count('id', filter=Q(is_active=True)),
            departments_with_staff=Count('id', filter=Q(staff__deleted_at__isnull=True, staff__is_active=True)),
            departments_with_manager=Count('id', filter=Q(manager__isnull=False))
        )

        # 角色分布
        role_distribution = {}
        for role_code, role_name in Staff.ROLE_CHOICES:
            role_distribution[role_code] = Staff.objects.filter(
                deleted_at__isnull=True, role=role_code
            ).count()

        # 部门员工分布
        department_distribution = Department.objects.filter(
            deleted_at__isnull=True
        ).annotate(
            staff_count=Count('staff', filter=Q(staff__deleted_at__isnull=True, staff__is_active=True))
        ).order_by('-staff_count')[:10]

        context.update({
            'db_stats': {
                'total_staff': staff_stats['total_staff'],
                'active_staff': staff_stats['active_staff'],
                'admin_staff': staff_stats['admin_staff'],
                'total_departments': department_stats['total_departments'],
            },
            'position_stats': {
                'total_positions': position_stats['total_positions'],
                'management_count': position_stats['management_count'],
                'department_manager_count': position_stats['department_manager_count'],
                'max_level': position_stats['max_level'] or 0,
                'departments_covered': position_stats['departments_covered'],
            },
            'department_stats': department_stats,
            'role_choices': Staff.ROLE_CHOICES,
            'role_distribution': role_distribution,
            'department_distribution': department_distribution,
        })

        return context


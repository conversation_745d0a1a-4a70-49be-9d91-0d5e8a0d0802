{% extends "admin/base_admin.html" %}

{% block page_title %}考评模板详情{% endblock %}
{% block page_description %}{{ object.name }} - 查看模板详细信息和评分项配置{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3">
    <a href="{% url 'evaluations:admin:template_list' %}" 
       class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700">
        <i data-lucide="arrow-left" class="w-4 h-4 mr-2 inline"></i>返回列表
    </a>
    {% if object.is_active %}
    <a href="{% url 'evaluations:admin:template_update' object.pk %}" 
       class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
        <i data-lucide="edit" class="w-4 h-4 mr-2 inline"></i>编辑模板
    </a>
    <a href="{% url 'evaluations:admin:template_copy' object.pk %}" 
       class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
        <i data-lucide="copy" class="w-4 h-4 mr-2 inline"></i>复制模板
    </a>
    {% endif %}
</div>
{% endblock %}

{% block admin_content %}
<!-- 模板基本信息 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 flex items-center">
            <i data-lucide="file-text" class="w-5 h-5 mr-2 text-gray-500"></i>
            模板基本信息
        </h3>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i data-lucide="file-text" class="w-8 h-8 text-blue-600"></i>
                    </div>
                    <div>
                        <h4 class="text-xl font-bold text-gray-900">{{ object.name }}</h4>
                        <p class="text-gray-600">{{ object.get_template_type_display }}</p>
                        <div class="flex items-center space-x-2 mt-2">
                            {% if object.is_default %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i data-lucide="star" class="w-3 h-3 mr-1"></i>默认模板
                            </span>
                            {% endif %}
                            {% if object.is_active %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>已启用
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i data-lucide="x-circle" class="w-3 h-3 mr-1"></i>已停用
                            </span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                {% if object.description %}
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">模板描述</label>
                    <div class="text-sm text-gray-600 bg-gray-50 p-3 rounded border">
                        <p>{{ object.description|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <i data-lucide="hash" class="w-5 h-5 text-blue-600"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-700">评分项数量</p>
                                <p class="text-xl font-bold text-blue-900">{{ object.get_items_count }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <i data-lucide="target" class="w-5 h-5 text-green-600"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-700">总分</p>
                                <p class="text-xl font-bold text-green-900">{{ object.calculate_total_score }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="time-card blue">
                        <div class="flex items-center mb-2">
                            <i data-lucide="calendar-plus" class="time-icon text-blue-600"></i>
                            <label class="block font-medium text-blue-700">创建时间</label>
                        </div>
                        <div class="space-y-1">
                            <p class="time-primary" data-relative-time="{{ object.created_at|date:'c' }}">{{ object.created_at|date:"Y年m月d日" }}</p>
                            <p class="time-secondary">{{ object.created_at|date:"H:i:s" }}</p>
                        </div>
                        <div class="time-status-indicator"></div>
                    </div>
                    <div class="time-card green">
                        <div class="flex items-center mb-2">
                            <i data-lucide="calendar-check" class="time-icon text-green-600"></i>
                            <label class="block font-medium text-green-700">最后更新</label>
                        </div>
                        <div class="space-y-1">
                            <p class="time-primary" data-relative-time="{{ object.updated_at|date:'c' }}">{{ object.updated_at|date:"Y年m月d日" }}</p>
                            <p class="time-secondary">{{ object.updated_at|date:"H:i:s" }}</p>
                        </div>
                        <div class="time-status-indicator"></div>
                    </div>
                </div>

                    <div>
                        <label class="block font-medium text-gray-700">排序权重</label>
                        <p class="text-gray-600">{{ object.sort_order }}</p>
                    </div>
                    <div>
                        <label class="block font-medium text-gray-700">模板ID</label>
                        <p class="text-gray-600">#{{ object.pk }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 评分项详情 -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 flex items-center">
                <i data-lucide="list-checks" class="w-5 h-5 mr-2 text-gray-500"></i>
                评分项目配置
            </h3>
            <div class="text-sm text-gray-500">
                共 {{ object.evaluationitem_set.count }} 个评分项
            </div>
        </div>
    </div>
    <div class="p-6">
        {% if object.evaluationitem_set.exists %}
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="border-b border-gray-200">
                        <th class="text-left py-3 px-4 font-medium text-gray-700">序号</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">评分项名称</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">评分方式</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">分值范围</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">权重</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">必填</th>
                        <th class="text-left py-3 px-4 font-medium text-gray-700">状态</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in object.evaluationitem_set.all %}
                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                        <td class="py-4 px-4">
                            <span class="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                                {{ item.sort_order|default:forloop.counter }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <div>
                                <p class="font-medium text-gray-900">{{ item.name }}</p>
                                {% if item.description %}
                                <p class="text-sm text-gray-500 mt-1">{{ item.description|truncatechars:50 }}</p>
                                {% endif %}
                            </div>
                        </td>
                        <td class="py-4 px-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {% if item.scoring_mode == 'score' %}bg-blue-100 text-blue-800
                                {% elif item.scoring_mode == 'level' %}bg-green-100 text-green-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ item.get_scoring_mode_display }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm text-gray-900">
                                {{ item.min_score|default:0 }} - {{ item.max_score|default:100 }}
                            </span>
                        </td>
                        <td class="py-4 px-4">
                            <span class="text-sm font-medium text-gray-900">{{ item.weight|default:1.0 }}</span>
                        </td>
                        <td class="py-4 px-4">
                            {% if item.is_required %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
                                <i data-lucide="asterisk" class="w-3 h-3 mr-1"></i>必填
                            </span>
                            {% else %}
                            <span class="text-sm text-gray-500">选填</span>
                            {% endif %}
                        </td>
                        <td class="py-4 px-4">
                            {% if item.is_active %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                <i data-lucide="check" class="w-3 h-3 mr-1"></i>启用
                            </span>
                            {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                <i data-lucide="x" class="w-3 h-3 mr-1"></i>禁用
                            </span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-12">
            <i data-lucide="list-x" class="w-16 h-16 text-gray-300 mx-auto mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">暂无评分项</h3>
            <p class="text-gray-500 mb-4">该模板还没有配置任何评分项目。</p>
            <a href="{% url 'evaluations:admin:template_update' object.pk %}" 
               class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 inline-flex items-center">
                <i data-lucide="plus" class="w-4 h-4 mr-2"></i>添加评分项
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}